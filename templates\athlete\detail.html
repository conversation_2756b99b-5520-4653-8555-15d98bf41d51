{% extends "layout.html" %}

{% block title %}运动员详情 - 运动会管理系统{% endblock %}

{% block page_title %}运动员详情{% endblock %}

{% block content %}
<div class="row">
    <!-- 运动员信息卡片 -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="bi bi-person"></i> 基本信息</h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-4">
                    <div class="avatar-placeholder bg-light rounded-circle mx-auto mb-3" style="width: 100px; height: 100px; line-height: 100px; font-size: 2.5rem;">
                        {% if athlete.gender == '男' %}
                            <i class="bi bi-person text-primary"></i>
                        {% else %}
                            <i class="bi bi-person-heart text-danger"></i>
                        {% endif %}
                    </div>
                    <h4>{{ athlete.name }}</h4>
                    <p class="text-muted">{{ athlete.student_id }}</p>
                </div>
                
                <ul class="list-group list-group-flush">
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span><i class="bi bi-gender-ambiguous me-2"></i> 性别</span>
                        <span class="badge bg-{% if athlete.gender == '男' %}primary{% else %}danger{% endif %} rounded-pill">{{ athlete.gender }}</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span><i class="bi bi-building me-2"></i> 所属院系</span>
                        <span>{{ athlete.department.dept_name }}</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span><i class="bi bi-code me-2"></i> 院系代码</span>
                        <span>{{ athlete.department.dept_code }}</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span><i class="bi bi-trophy me-2"></i> 参与项目数</span>
                        <span class="badge bg-info rounded-pill">{{ scores|length }}</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span><i class="bi bi-award me-2"></i> 总得分</span>
                        <span class="badge bg-success rounded-pill">{{ scores|sum(attribute='points') or 0 }}</span>
                    </li>
                </ul>
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between">
                    <a href="{{ url_for('athlete.index') }}" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i> 返回
                    </a>
                    <a href="{{ url_for('athlete.edit', athlete_id=athlete.athlete_id) }}" class="btn btn-primary">
                        <i class="bi bi-pencil"></i> 编辑
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 成绩列表卡片 -->
    <div class="col-md-8 mb-4">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0"><i class="bi bi-clipboard-data"></i> 比赛成绩</h5>
            </div>
            <div class="card-body">
                {% if scores %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>比赛项目</th>
                                <th>项目类型</th>
                                <th>成绩</th>
                                <th>名次</th>
                                <th>得分</th>
                                <th>记录时间</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for score in scores %}
                            <tr>
                                <td>
                                    <a href="{{ url_for('event.detail', event_id=score.event.event_id) }}">
                                        {{ score.event.event_name }}
                                    </a>
                                </td>
                                <td>{{ score.event.event_type }}</td>
                                <td>{{ score.result }}</td>
                                <td>
                                    {% if score.ranking %}
                                        {% if score.ranking == 1 %}
                                            <span class="badge bg-warning">第{{ score.ranking }}名</span>
                                        {% elif score.ranking == 2 %}
                                            <span class="badge bg-secondary">第{{ score.ranking }}名</span>
                                        {% elif score.ranking == 3 %}
                                            <span class="badge bg-danger">第{{ score.ranking }}名</span>
                                        {% else %}
                                            <span class="badge bg-light text-dark">第{{ score.ranking }}名</span>
                                        {% endif %}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>{{ score.points or 0 }}</td>
                                <td>{{ score.record_time.strftime('%Y-%m-%d %H:%M') }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i> 该运动员暂无比赛成绩记录
                </div>
                {% endif %}
            </div>
            {% if session.role == 'admin' %}
            <div class="card-footer">
                <a href="{{ url_for('score.add') }}" class="btn btn-success">
                    <i class="bi bi-plus-circle"></i> 添加成绩
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
