{% extends "layout.html" %}

{% block title %}编辑运动员 - 运动会管理系统{% endblock %}

{% block page_title %}编辑运动员{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">编辑运动员</h5>
    </div>
    <div class="card-body">
        <form method="post" action="{{ url_for('athlete.edit', athlete_id=athlete.athlete_id) }}">
            {{ form.csrf_token }}
            
            <div class="mb-3">
                <label for="name" class="form-label">姓名</label>
                {{ form.name(class="form-control", id="name") }}
                {% if form.name.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.name.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
            
            <div class="mb-3">
                <label for="gender" class="form-label">性别</label>
                {{ form.gender(class="form-select", id="gender") }}
                {% if form.gender.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.gender.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
            
            <div class="mb-3">
                <label for="student_id" class="form-label">学号</label>
                {{ form.student_id(class="form-control", id="student_id") }}
                {% if form.student_id.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.student_id.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% endif %}
                <div class="form-text">学号必须唯一，用于标识运动员</div>
            </div>
            
            <div class="mb-3">
                <label for="dept_id" class="form-label">所属院系</label>
                {{ form.dept_id(class="form-select", id="dept_id") }}
                {% if form.dept_id.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.dept_id.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
            
            <div class="d-flex justify-content-between">
                <a href="{{ url_for('athlete.index') }}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> 返回
                </a>
                {{ form.submit(class="btn btn-primary") }}
            </div>
        </form>
    </div>
</div>
{% endblock %}
