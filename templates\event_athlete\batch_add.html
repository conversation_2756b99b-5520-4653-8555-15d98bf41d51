{% extends "layout.html" %}

{% block title %}批量添加学生到比赛项目 - 运动会管理系统{% endblock %}

{% block page_title %}批量添加学生到比赛项目{% endblock %}

{% block content %}
<div class="card shadow-sm">
    <div class="card-header bg-white py-3">
        <h5 class="mb-0 text-primary">
            <i class="bi bi-people-fill me-2"></i>批量添加学生到比赛项目
        </h5>
    </div>
    <div class="card-body">
        <form method="post" action="{{ url_for('event_athlete.batch_add') }}">
            {{ form.csrf_token }}
            
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="event_id" class="form-label">比赛项目</label>
                        {{ form.event_id(class="form-select", id="event_id") }}
                        {% if form.event_id.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.event_id.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="department_id" class="form-label">院系筛选</label>
                        {{ form.department_id(class="form-select", id="department_id") }}
                        {% if form.department_id.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.department_id.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="alert alert-info mb-4">
                <div class="d-flex">
                    <div class="me-3">
                        <i class="bi bi-info-circle-fill" style="font-size: 1.5rem;"></i>
                    </div>
                    <div>
                        <h5 class="alert-heading">提示</h5>
                        <p class="mb-0">
                            1. 选择比赛项目和院系后，下方会显示符合条件的运动员列表<br>
                            2. 勾选需要添加的运动员，然后点击"提交选中学生"按钮<br>
                            3. 成绩信息可以稍后在学生列表页面填写
                        </p>
                    </div>
                </div>
            </div>
            
            {% if athletes %}
            <div class="mb-3">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0">符合条件的运动员列表 ({{ athletes|length }}人)</h6>
                    <div>
                        <button type="button" id="select-all" class="btn btn-sm btn-outline-primary me-2">全选</button>
                        <button type="button" id="deselect-all" class="btn btn-sm btn-outline-secondary">取消全选</button>
                    </div>
                </div>
                
                <div class="table-responsive">
                    <table class="table table-hover" id="athletes-table">
                        <thead class="table-light">
                            <tr>
                                <th width="50">选择</th>
                                <th>ID</th>
                                <th>姓名</th>
                                <th>学号</th>
                                <th>性别</th>
                                <th>院系</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for athlete in athletes %}
                            <tr>
                                <td>
                                    <div class="form-check">
                                        <input class="form-check-input athlete-checkbox" type="checkbox" name="selected_athletes" value="{{ athlete.athlete_id }}" id="athlete-{{ athlete.athlete_id }}">
                                    </div>
                                </td>
                                <td>{{ athlete.athlete_id }}</td>
                                <td>{{ athlete.name }}</td>
                                <td>{{ athlete.student_id }}</td>
                                <td>{{ athlete.gender }}</td>
                                <td>{{ athlete.department.dept_name }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <div class="d-flex justify-content-between mt-4">
                    <a href="{{ url_for('event_athlete.index', event_id=selected_event_id) }}" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i> 返回
                    </a>
                    {{ form.submit(class="btn btn-primary") }}
                </div>
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="bi bi-search text-muted" style="font-size: 3rem;"></i>
                <p class="mt-3 text-muted">
                    {% if selected_event_id %}
                    没有找到符合条件的运动员，或所有符合条件的运动员已经添加到该比赛项目
                    {% else %}
                    请选择比赛项目
                    {% endif %}
                </p>
                <a href="{{ url_for('event_athlete.index', event_id=selected_event_id) }}" class="btn btn-secondary mt-2">
                    <i class="bi bi-arrow-left"></i> 返回
                </a>
            </div>
            {% endif %}
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // 初始化Select2
        $('#event_id, #department_id').select2({
            theme: 'bootstrap-5',
            width: '100%'
        });
        
        // 初始化DataTable
        $('#athletes-table').DataTable({
            language: {
                url: "{{ url_for('static', filename='js/dataTables.chinese.json') }}"
            },
            responsive: true,
            pageLength: 25,
            lengthMenu: [[10, 25, 50, -1], [10, 25, 50, "全部"]]
        });
        
        // 全选按钮
        $('#select-all').on('click', function() {
            $('.athlete-checkbox').prop('checked', true);
        });
        
        // 取消全选按钮
        $('#deselect-all').on('click', function() {
            $('.athlete-checkbox').prop('checked', false);
        });
        
        // 当比赛项目或院系改变时，刷新页面
        $('#event_id, #department_id').on('change', function() {
            const eventId = $('#event_id').val();
            const departmentId = $('#department_id').val();
            
            if (!eventId) return;
            
            // 构建URL
            let url = "{{ url_for('event_athlete.batch_add') }}?event_id=" + eventId;
            if (departmentId && departmentId != '0') {
                url += "&department_id=" + departmentId;
            }
            
            // 跳转到新URL
            window.location.href = url;
        });
        
        // 表单提交前验证
        $('form').on('submit', function(e) {
            const checkedCount = $('.athlete-checkbox:checked').length;
            
            if (checkedCount === 0) {
                e.preventDefault();
                alert('请至少选择一名运动员！');
                return false;
            }
            
            return true;
        });
    });
</script>
{% endblock %}
