{% extends "layout.html" %}

{% block title %}院系排名 - 运动会管理系统{% endblock %}

{% block page_title %}院系排名{% endblock %}

{% block styles %}
<style>
    .ranking-card {
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s;
    }
    .ranking-card:hover {
        transform: translateY(-5px);
    }
    .medal-icon {
        font-size: 2rem;
        margin-right: 10px;
    }
    .gold {
        color: #ffc107;
    }
    .silver {
        color: #adb5bd;
    }
    .bronze {
        color: #cd7f32;
    }
    .ranking-table th, .ranking-table td {
        vertical-align: middle;
    }
    .progress {
        height: 10px;
        border-radius: 5px;
    }
</style>
{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header bg-info text-white">
        <h5 class="mb-0"><i class="bi bi-bar-chart"></i> 院系得分排名</h5>
    </div>
    <div class="card-body">
        {% if rankings %}
        <!-- 前三名卡片 -->
        <div class="row mb-4">
            {% for rank in rankings[:3] %}
                {% if loop.index == 1 %}
                    <div class="col-md-4 mb-3">
                        <div class="card ranking-card bg-warning text-dark h-100">
                            <div class="card-body text-center">
                                <h1><i class="bi bi-trophy-fill medal-icon gold"></i></h1>
                                <h3>第一名</h3>
                                <h4>{{ rank.dept_name }}</h4>
                                <p class="mb-0">{{ rank.dept_code }}</p>
                                <h2 class="mt-3">{{ rank.total_points }} 分</h2>
                            </div>
                        </div>
                    </div>
                {% elif loop.index == 2 %}
                    <div class="col-md-4 mb-3">
                        <div class="card ranking-card bg-light h-100">
                            <div class="card-body text-center">
                                <h1><i class="bi bi-trophy-fill medal-icon silver"></i></h1>
                                <h3>第二名</h3>
                                <h4>{{ rank.dept_name }}</h4>
                                <p class="mb-0">{{ rank.dept_code }}</p>
                                <h2 class="mt-3">{{ rank.total_points }} 分</h2>
                            </div>
                        </div>
                    </div>
                {% elif loop.index == 3 %}
                    <div class="col-md-4 mb-3">
                        <div class="card ranking-card bg-danger text-white h-100">
                            <div class="card-body text-center">
                                <h1><i class="bi bi-trophy-fill medal-icon bronze"></i></h1>
                                <h3>第三名</h3>
                                <h4>{{ rank.dept_name }}</h4>
                                <p class="mb-0">{{ rank.dept_code }}</p>
                                <h2 class="mt-3">{{ rank.total_points }} 分</h2>
                            </div>
                        </div>
                    </div>
                {% endif %}
            {% endfor %}
        </div>
        
        <!-- 排名表格 -->
        <div class="table-responsive">
            <table class="table table-striped table-hover ranking-table">
                <thead>
                    <tr>
                        <th>排名</th>
                        <th>院系代码</th>
                        <th>院系名称</th>
                        <th>总得分</th>
                        <th>得分比例</th>
                    </tr>
                </thead>
                <tbody>
                    {% set max_points = rankings[0].total_points %}
                    {% for rank in rankings %}
                    <tr>
                        <td>
                            {% if loop.index == 1 %}
                                <span class="badge bg-warning text-dark">第{{ loop.index }}名</span>
                            {% elif loop.index == 2 %}
                                <span class="badge bg-secondary">第{{ loop.index }}名</span>
                            {% elif loop.index == 3 %}
                                <span class="badge bg-danger">第{{ loop.index }}名</span>
                            {% else %}
                                <span class="badge bg-light text-dark">第{{ loop.index }}名</span>
                            {% endif %}
                        </td>
                        <td>{{ rank.dept_code }}</td>
                        <td>{{ rank.dept_name }}</td>
                        <td>{{ rank.total_points }}</td>
                        <td style="width: 30%;">
                            <div class="progress">
                                <div class="progress-bar 
                                    {% if loop.index == 1 %}
                                        bg-warning
                                    {% elif loop.index == 2 %}
                                        bg-secondary
                                    {% elif loop.index == 3 %}
                                        bg-danger
                                    {% else %}
                                        bg-info
                                    {% endif %}" 
                                    role="progressbar" 
                                    style="width: {{ (rank.total_points / max_points * 100)|round }}%;" 
                                    aria-valuenow="{{ (rank.total_points / max_points * 100)|round }}" 
                                    aria-valuemin="0" 
                                    aria-valuemax="100">
                                </div>
                            </div>
                            <small>{{ (rank.total_points / max_points * 100)|round }}%</small>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="alert alert-info">
            <i class="bi bi-info-circle"></i> 暂无院系得分数据
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
