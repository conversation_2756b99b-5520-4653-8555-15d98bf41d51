{% extends "layout.html" %}

{% block title %}成绩管理 - 运动会管理系统{% endblock %}

{% block page_title %}成绩管理{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">成绩列表</h5>
        {% if session.role == 'admin' %}
        <a href="{{ url_for('score.add') }}" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> 添加成绩
        </a>
        {% endif %}
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover datatable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>运动员</th>
                        <th>学号</th>
                        <th>院系</th>
                        <th>比赛项目</th>
                        <th>成绩</th>
                        <th>名次</th>
                        <th>得分</th>
                        <th>记录时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="score-table">
                    <tr>
                        <td colspan="10" class="text-center">加载中...</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 加载成绩数据
        loadScoreData();
    });

    // 加载成绩数据
    function loadScoreData() {
        fetch('/score/api/list')
            .then(response => response.json())
            .then(data => {
                const tableBody = document.getElementById('score-table');
                tableBody.innerHTML = '';

                if (data.length === 0) {
                    const row = document.createElement('tr');
                    row.innerHTML = '<td colspan="10" class="text-center">暂无成绩数据</td>';
                    tableBody.appendChild(row);
                } else {
                    data.forEach(score => {
                        const row = document.createElement('tr');

                        // 构建操作按钮
                        let actions = `
                            <a href="{{ url_for('score.edit', score_id=0) }}${score.score_id}" class="btn btn-sm btn-primary me-1"><i class="bi bi-pencil"></i> 编辑</a>
                            <form method="POST" action="{{ url_for('score.delete', score_id=0) }}${score.score_id}" class="d-inline">
                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                <button type="submit" class="btn btn-sm btn-danger delete-confirm"><i class="bi bi-trash"></i> 删除</button>
                            </form>
                        `;

                        // 名次标签
                        let rankingBadge = '';
                        if (score.ranking) {
                            if (score.ranking === 1) {
                                rankingBadge = '<span class="badge bg-warning">第1名</span>';
                            } else if (score.ranking === 2) {
                                rankingBadge = '<span class="badge bg-secondary">第2名</span>';
                            } else if (score.ranking === 3) {
                                rankingBadge = '<span class="badge bg-danger">第3名</span>';
                            } else {
                                rankingBadge = `<span class="badge bg-light text-dark">第${score.ranking}名</span>`;
                            }
                        } else {
                            rankingBadge = '-';
                        }

                        row.innerHTML = `
                            <td>${score.score_id}</td>
                            <td><a href="{{ url_for('athlete.detail', athlete_id=0) }}${score.athlete_id}">${score.athlete_name}</a></td>
                            <td>${score.student_id}</td>
                            <td>${score.dept_name}</td>
                            <td><a href="{{ url_for('event.detail', event_id=0) }}${score.event_id}">${score.event_name}</a></td>
                            <td>${score.result}</td>
                            <td>${rankingBadge}</td>
                            <td>${score.points || 0}</td>
                            <td>${score.record_time}</td>
                            <td>${actions}</td>
                        `;

                        tableBody.appendChild(row);
                    });

                    // 初始化删除确认
                    document.querySelectorAll('.delete-confirm').forEach(function(button) {
                        button.addEventListener('click', function(e) {
                            if (!confirm('确定要删除该成绩记录吗？此操作不可撤销！')) {
                                e.preventDefault();
                            }
                        });
                    });

                    // 初始化DataTable
                    if ($.fn.DataTable.isDataTable('.datatable')) {
                        $('.datatable').DataTable().destroy();
                    }

                    $('.datatable').DataTable({
                        language: {
                            url: '/static/js/dataTables.chinese.json'
                        },
                        responsive: true,
                        pageLength: 10,
                        lengthMenu: [[10, 25, 50, -1], [10, 25, 50, "全部"]]
                    });
                }
            })
            .catch(error => {
                console.error('Error loading score data:', error);
                const tableBody = document.getElementById('score-table');
                tableBody.innerHTML = '<tr><td colspan="10" class="text-center text-danger">加载数据失败</td></tr>';
            });
    }
</script>
{% endblock %}
