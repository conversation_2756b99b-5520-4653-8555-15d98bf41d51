{% extends "layout.html" %}

{% block title %}添加裁判员 - 运动会管理系统{% endblock %}

{% block page_title %}添加裁判员{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">添加新裁判员</h5>
    </div>
    <div class="card-body">
        <form method="post" action="{{ url_for('judge.add') }}">
            {{ form.csrf_token }}
            
            <div class="mb-3">
                <label for="name" class="form-label">姓名</label>
                {{ form.name(class="form-control", id="name") }}
                {% if form.name.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.name.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
            
            <div class="mb-3">
                <label for="level" class="form-label">级别</label>
                {{ form.level(class="form-control", id="level") }}
                {% if form.level.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.level.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% endif %}
                <div class="form-text">可选，例如：国家级、一级、二级等</div>
            </div>
            
            <div class="mb-3">
                <label for="contact" class="form-label">联系方式</label>
                {{ form.contact(class="form-control", id="contact") }}
                {% if form.contact.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.contact.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% endif %}
                <div class="form-text">可选，例如：电话号码、邮箱等</div>
            </div>
            
            <div class="d-flex justify-content-between">
                <a href="{{ url_for('judge.index') }}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> 返回
                </a>
                {{ form.submit(class="btn btn-primary") }}
            </div>
        </form>
    </div>
</div>
{% endblock %}
