"""
裁判员管理模块
"""
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_wtf import FlaskForm
from wtforms import StringField, SubmitField
from wtforms.validators import DataRequired, Length, Optional
from modules.database import db
from modules.models import Judge, EventJudge
from modules.auth import login_required, admin_required

# 创建裁判员蓝图
judge_bp = Blueprint('judge', __name__)

# 裁判员表单类
class JudgeForm(FlaskForm):
    """裁判员表单类，用于添加和编辑裁判员信息"""
    name = StringField('姓名', validators=[DataRequired(), Length(max=50)])
    level = StringField('级别', validators=[Optional(), Length(max=20)])
    contact = StringField('联系方式', validators=[Optional(), Length(max=20)])
    submit = SubmitField('提交')

@judge_bp.route('/')
@login_required
def index():
    """
    裁判员列表页面

    Returns:
        裁判员列表页面
    """
    # 使用标准表格组件
    return render_template('judge/index.html')

@judge_bp.route('/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add():
    """
    添加裁判员

    Returns:
        添加裁判员页面或重定向到裁判员列表页面
    """
    # 创建表单
    form = JudgeForm()

    # 处理表单提交
    if form.validate_on_submit():
        # 获取表单数据
        name = form.name.data
        level = form.level.data
        contact = form.contact.data

        # 创建裁判员
        judge = Judge(name=name, level=level, contact=contact)

        # 保存到数据库
        db.session.add(judge)
        db.session.commit()

        # 重定向到裁判员列表页面
        flash('裁判员添加成功！', 'success')
        return redirect(url_for('judge.index'))

    # 渲染添加裁判员页面
    return render_template('judge/add.html', form=form)

@judge_bp.route('/edit/<int:judge_id>', methods=['GET', 'POST'])
@login_required
@admin_required
def edit(judge_id):
    """
    编辑裁判员

    Args:
        judge_id: 裁判员ID

    Returns:
        编辑裁判员页面或重定向到裁判员列表页面
    """
    # 查询裁判员
    judge = Judge.query.get_or_404(judge_id)

    # 创建表单
    form = JudgeForm(obj=judge)

    # 处理表单提交
    if form.validate_on_submit():
        # 获取表单数据
        name = form.name.data
        level = form.level.data
        contact = form.contact.data

        # 更新裁判员
        judge.name = name
        judge.level = level
        judge.contact = contact

        # 保存到数据库
        db.session.commit()

        # 重定向到裁判员列表页面
        flash('裁判员更新成功！', 'success')
        return redirect(url_for('judge.index'))

    # 渲染编辑裁判员页面
    return render_template('judge/edit.html', form=form, judge=judge)

@judge_bp.route('/delete/<int:judge_id>', methods=['POST'])
@login_required
@admin_required
def delete(judge_id):
    """
    删除裁判员

    Args:
        judge_id: 裁判员ID

    Returns:
        重定向到裁判员列表页面
    """
    # 查询裁判员
    judge = Judge.query.get_or_404(judge_id)

    # 检查是否有关联的比赛项目
    event_judges = EventJudge.query.filter_by(judge_id=judge_id).all()
    if event_judges:
        flash('无法删除裁判员，因为有关联的比赛项目！', 'danger')
        return redirect(url_for('judge.index'))

    # 删除裁判员
    db.session.delete(judge)
    db.session.commit()

    # 重定向到裁判员列表页面
    flash('裁判员删除成功！', 'success')
    return redirect(url_for('judge.index'))

@judge_bp.route('/api/list')
@login_required
def api_list():
    """
    裁判员列表API
    用于AJAX请求获取裁判员列表

    Returns:
        JSON格式的裁判员列表
    """
    judges = Judge.query.all()
    result = []

    for judge in judges:
        # 查询裁判员关联的项目数量
        event_count = EventJudge.query.filter_by(judge_id=judge.judge_id).count()

        result.append({
            'judge_id': judge.judge_id,
            'name': judge.name,
            'level': judge.level,
            'contact': judge.contact,
            'event_count': event_count
        })

    return jsonify(result)
