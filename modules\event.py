"""
比赛项目管理模块
"""
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_wtf import FlaskForm
from wtforms import StringField, SelectField, IntegerField, DateTimeField, SubmitField
from wtforms.validators import DataRequired, Length, Optional, NumberRange
from modules.database import db
from modules.models import Event, Judge, EventJudge, Score
from modules.auth import login_required, admin_required
from datetime import datetime

# 创建比赛项目蓝图
event_bp = Blueprint('event', __name__)

# 比赛项目表单类
class EventForm(FlaskForm):
    """比赛项目表单类，用于添加和编辑比赛项目信息"""
    event_name = StringField('项目名称', validators=[DataRequired(), Length(max=100)])
    event_type = SelectField('项目类型', choices=[('田赛', '田赛'), ('径赛', '径赛'), ('球类', '球类'), ('游泳', '游泳')], validators=[DataRequired()])
    gender_limit = SelectField('性别限制', choices=[('男', '男'), ('女', '女'), ('不限', '不限')], default='不限')
    max_participants = IntegerField('最大参与人数', validators=[Optional(), NumberRange(min=1)])
    start_time = DateTimeField('开始时间', format='%Y-%m-%dT%H:%M', validators=[Optional()])
    location = StringField('比赛地点', validators=[Optional(), Length(max=50)])
    submit = SubmitField('提交')

@event_bp.route('/')
@login_required
def index():
    """
    比赛项目列表页面

    Returns:
        比赛项目列表页面
    """
    # 使用标准表格组件
    return render_template('event/index.html')

@event_bp.route('/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add():
    """
    添加比赛项目

    Returns:
        添加比赛项目页面或重定向到比赛项目列表页面
    """
    # 创建表单
    form = EventForm()

    # 处理表单提交
    if form.validate_on_submit():
        # 获取表单数据
        event_name = form.event_name.data
        event_type = form.event_type.data
        gender_limit = form.gender_limit.data
        max_participants = form.max_participants.data
        start_time = form.start_time.data
        location = form.location.data

        # 创建比赛项目
        event = Event(
            event_name=event_name,
            event_type=event_type,
            gender_limit=gender_limit,
            max_participants=max_participants,
            start_time=start_time,
            location=location
        )

        # 保存到数据库
        db.session.add(event)
        db.session.commit()

        # 重定向到比赛项目裁判员分配页面
        flash('比赛项目添加成功！请继续分配裁判员。', 'success')
        return redirect(url_for('event.add_judge', event_id=event.event_id))

    # 渲染添加比赛项目页面
    return render_template('event/add.html', form=form)

@event_bp.route('/edit/<int:event_id>', methods=['GET', 'POST'])
@login_required
@admin_required
def edit(event_id):
    """
    编辑比赛项目

    Args:
        event_id: 比赛项目ID

    Returns:
        编辑比赛项目页面或重定向到比赛项目列表页面
    """
    # 查询比赛项目
    event = Event.query.get_or_404(event_id)

    # 创建表单
    form = EventForm(obj=event)

    # 如果有开始时间，确保格式正确
    if event.start_time:
        # 将datetime对象转换为HTML5 datetime-local格式
        form.start_time.data = event.start_time

    # 处理表单提交
    if form.validate_on_submit():
        # 获取表单数据
        event_name = form.event_name.data
        event_type = form.event_type.data
        gender_limit = form.gender_limit.data
        max_participants = form.max_participants.data
        start_time = form.start_time.data
        location = form.location.data

        # 更新比赛项目
        event.event_name = event_name
        event.event_type = event_type
        event.gender_limit = gender_limit
        event.max_participants = max_participants
        event.start_time = start_time
        event.location = location

        # 保存到数据库
        db.session.commit()

        # 重定向到比赛项目列表页面
        flash('比赛项目更新成功！', 'success')
        return redirect(url_for('event.index'))

    # 渲染编辑比赛项目页面
    return render_template('event/edit.html', form=form, event=event)

@event_bp.route('/delete/<int:event_id>', methods=['POST'])
@login_required
@admin_required
def delete(event_id):
    """
    删除比赛项目

    Args:
        event_id: 比赛项目ID

    Returns:
        重定向到比赛项目列表页面
    """
    # 查询比赛项目
    event = Event.query.get_or_404(event_id)

    # 检查是否有关联的成绩
    if event.scores:
        flash('无法删除比赛项目，因为有关联的成绩记录！', 'danger')
        return redirect(url_for('event.index'))

    # 删除比赛项目与裁判的关联
    EventJudge.query.filter_by(event_id=event_id).delete()

    # 删除比赛项目
    db.session.delete(event)
    db.session.commit()

    # 重定向到比赛项目列表页面
    flash('比赛项目删除成功！', 'success')
    return redirect(url_for('event.index'))

@event_bp.route('/detail/<int:event_id>')
@login_required
def detail(event_id):
    """
    比赛项目详情页面

    Args:
        event_id: 比赛项目ID

    Returns:
        比赛项目详情页面
    """
    # 查询比赛项目
    event = Event.query.get_or_404(event_id)

    # 查询比赛项目的裁判
    event_judges = EventJudge.query.filter_by(event_id=event_id).all()
    judges = []
    for event_judge in event_judges:
        judge = Judge.query.get(event_judge.judge_id)
        if judge:
            judges.append({
                'judge': judge,
                'role': event_judge.role
            })

    # 查询比赛项目的成绩
    scores = Score.query.filter_by(event_id=event_id).order_by(Score.ranking).all()

    return render_template('event/detail.html', event=event, judges=judges, scores=scores)

@event_bp.route('/api/list')
@login_required
def api_list():
    """
    比赛项目列表API
    用于AJAX请求获取比赛项目列表

    Returns:
        JSON格式的比赛项目列表
    """
    events = Event.query.all()
    result = []

    for event in events:
        # 查询项目关联的裁判员数量
        judge_count = EventJudge.query.filter_by(event_id=event.event_id).count()
        # 查询项目的成绩数量
        score_count = Score.query.filter_by(event_id=event.event_id).count()

        result.append({
            'event_id': event.event_id,
            'event_name': event.event_name,
            'event_type': event.event_type,
            'gender_limit': event.gender_limit,
            'max_participants': event.max_participants,
            'start_time': event.start_time.strftime('%Y-%m-%d %H:%M') if event.start_time else '',
            'location': event.location,
            'judge_count': judge_count,
            'score_count': score_count
        })

    return jsonify(result)

# 比赛项目裁判关联表单类
class EventJudgeForm(FlaskForm):
    """比赛项目裁判关联表单类，用于添加裁判到比赛项目"""
    judge_id = SelectField('裁判', coerce=int, validators=[DataRequired()])
    role = StringField('角色', validators=[DataRequired(), Length(max=20)])
    submit = SubmitField('添加裁判')

    def __init__(self, *args, **kwargs):
        super(EventJudgeForm, self).__init__(*args, **kwargs)
        self.judge_id.choices = [(judge.judge_id, judge.name) for judge in Judge.query.all()]

@event_bp.route('/<int:event_id>/add_judge', methods=['GET', 'POST'])
@login_required
@admin_required
def add_judge(event_id):
    """
    添加裁判到比赛项目

    Args:
        event_id: 比赛项目ID

    Returns:
        添加裁判页面或重定向到比赛项目详情页面
    """
    # 查询比赛项目
    event = Event.query.get_or_404(event_id)

    # 创建表单
    form = EventJudgeForm()

    # 处理表单提交
    if form.validate_on_submit():
        # 获取表单数据
        judge_id = form.judge_id.data
        role = form.role.data

        # 检查裁判是否已关联到该比赛项目
        existing_event_judge = EventJudge.query.filter_by(event_id=event_id, judge_id=judge_id).first()
        if existing_event_judge:
            flash('该裁判已关联到此比赛项目！', 'danger')
            return render_template('event/add_judge.html', form=form, event=event)

        # 创建比赛项目裁判关联
        event_judge = EventJudge(event_id=event_id, judge_id=judge_id, role=role)

        # 保存到数据库
        db.session.add(event_judge)
        db.session.commit()

        # 重定向到比赛项目裁判员添加页面，可以继续添加裁判员
        flash('裁判添加成功！您可以继续添加更多裁判员。', 'success')
        return redirect(url_for('event.add_judge', event_id=event_id))

    # 渲染添加裁判页面
    return render_template('event/add_judge.html', form=form, event=event)

@event_bp.route('/<int:event_id>/remove_judge/<int:judge_id>', methods=['POST'])
@login_required
@admin_required
def remove_judge(event_id, judge_id):
    """
    从比赛项目中移除裁判

    Args:
        event_id: 比赛项目ID
        judge_id: 裁判ID

    Returns:
        重定向到比赛项目详情页面
    """
    # 查询比赛项目裁判关联
    event_judge = EventJudge.query.filter_by(event_id=event_id, judge_id=judge_id).first_or_404()

    # 删除比赛项目裁判关联
    db.session.delete(event_judge)
    db.session.commit()

    # 重定向到比赛项目详情页面
    flash('裁判移除成功！', 'success')
    return redirect(url_for('event.detail', event_id=event_id))
