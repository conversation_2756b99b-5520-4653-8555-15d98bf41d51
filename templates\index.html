{% extends "layout.html" %}

{% block title %}首页 - 运动会管理系统{% endblock %}

{% block page_title %}系统概览{% endblock %}

{% block styles %}
<style>
    /* 仪表板特定样式 */
    .dashboard-container {
        animation: fadeIn 0.5s ease;
    }

    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    .quick-action {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 1.5rem;
        background-color: #fff;
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
        transition: all var(--transition-speed) ease;
        text-decoration: none;
        color: var(--text-color);
        height: 100%;
    }

    .quick-action:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        color: var(--primary-color);
    }

    .quick-action-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background-color: var(--primary-light);
        color: var(--primary-color);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin-bottom: 1rem;
        transition: all var(--transition-speed) ease;
    }

    .quick-action:hover .quick-action-icon {
        background-color: var(--primary-color);
        color: #fff;
    }

    .quick-action-title {
        font-weight: 600;
        font-size: 1.1rem;
        margin-bottom: 0.5rem;
        text-align: center;
    }

    .quick-action-desc {
        font-size: 0.9rem;
        color: var(--text-secondary);
        text-align: center;
        margin-bottom: 0;
    }

    .data-card {
        height: 100%;
    }

    .data-table th {
        font-weight: 600;
        color: var(--text-color);
    }

    .data-table td {
        vertical-align: middle;
    }

    .data-table a {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 500;
    }

    .data-table a:hover {
        color: var(--primary-hover);
        text-decoration: underline;
    }

    .rank-badge {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 28px;
        height: 28px;
        border-radius: 50%;
        background-color: var(--light-bg);
        color: var(--text-color);
        font-weight: 600;
        font-size: 0.85rem;
    }

    .rank-1 {
        background-color: #FFD700;
        color: #fff;
    }

    .rank-2 {
        background-color: #C0C0C0;
        color: #fff;
    }

    .rank-3 {
        background-color: #CD7F32;
        color: #fff;
    }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-container">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
        <h1 class="welcome-title">欢迎回来，{{ session.username }}</h1>
        <p class="welcome-subtitle">运动会管理系统仪表板概览</p>
        <p class="current-date" id="current-date">{{ now.strftime('%Y年%m月%d日 %H:%M:%S') }}</p>
    </div>

    <!-- 统计卡片 -->
    <div class="row mb-4" id="stats-container">
        <div class="col-md-4 col-lg-2 mb-4">
            <div class="stat-card department-card">
                <div class="stat-icon">
                    <i class="bi bi-building"></i>
                </div>
                <div class="stat-number" id="department-count">-</div>
                <div class="stat-label">院系数量</div>
                <div class="stat-progress">
                    <div class="progress">
                        <div class="progress-bar progress-bar-department" style="width: 100%"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4 col-lg-2 mb-4">
            <div class="stat-card athlete-card">
                <div class="stat-icon">
                    <i class="bi bi-person"></i>
                </div>
                <div class="stat-number" id="athlete-count">-</div>
                <div class="stat-label">运动员数量</div>
                <div class="stat-progress">
                    <div class="progress">
                        <div class="progress-bar progress-bar-athlete" style="width: 100%"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4 col-lg-2 mb-4">
            <div class="stat-card event-card">
                <div class="stat-icon">
                    <i class="bi bi-calendar-event"></i>
                </div>
                <div class="stat-number" id="event-count">-</div>
                <div class="stat-label">比赛项目数量</div>
                <div class="stat-progress">
                    <div class="progress">
                        <div class="progress-bar progress-bar-event" style="width: 100%"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4 col-lg-2 mb-4">
            <div class="stat-card judge-card">
                <div class="stat-icon">
                    <i class="bi bi-flag"></i>
                </div>
                <div class="stat-number" id="judge-count">-</div>
                <div class="stat-label">裁判员数量</div>
                <div class="stat-progress">
                    <div class="progress">
                        <div class="progress-bar progress-bar-judge" style="width: 100%"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4 col-lg-2 mb-4">
            <div class="stat-card score-card">
                <div class="stat-icon">
                    <i class="bi bi-clipboard-data"></i>
                </div>
                <div class="stat-number" id="score-count">-</div>
                <div class="stat-label">成绩记录数量</div>
                <div class="stat-progress">
                    <div class="progress">
                        <div class="progress-bar progress-bar-score" style="width: 100%"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4 col-lg-2 mb-4">
            <div class="stat-card event-card">
                <div class="stat-icon">
                    <i class="bi bi-calendar-check"></i>
                </div>
                <div class="stat-number" id="today-events">-</div>
                <div class="stat-label">今日比赛数量</div>
                <div class="stat-progress">
                    <div class="progress">
                        <div class="progress-bar progress-bar-event" style="width: 100%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 快速操作 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="data-section">
                <div class="data-section-title">
                    <h3><i class="bi bi-lightning-charge me-2"></i>快速操作</h3>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-3 mb-4">
            <a href="{{ url_for('athlete.add') }}" class="quick-action">
                <div class="quick-action-icon">
                    <i class="bi bi-person-plus"></i>
                </div>
                <div class="quick-action-title">添加运动员</div>
                <div class="quick-action-desc">注册新的运动员信息</div>
            </a>
        </div>
        <div class="col-md-6 col-lg-3 mb-4">
            <a href="{{ url_for('event.add') }}" class="quick-action">
                <div class="quick-action-icon">
                    <i class="bi bi-calendar-plus"></i>
                </div>
                <div class="quick-action-title">添加比赛项目</div>
                <div class="quick-action-desc">创建新的比赛项目</div>
            </a>
        </div>
        <div class="col-md-6 col-lg-3 mb-4">
            <a href="{{ url_for('score.add') }}" class="quick-action">
                <div class="quick-action-icon">
                    <i class="bi bi-clipboard-plus"></i>
                </div>
                <div class="quick-action-title">录入比赛成绩</div>
                <div class="quick-action-desc">记录运动员的比赛成绩</div>
            </a>
        </div>
        <div class="col-md-6 col-lg-3 mb-4">
            <a href="{{ url_for('score.department_ranking') }}" class="quick-action">
                <div class="quick-action-icon">
                    <i class="bi bi-bar-chart"></i>
                </div>
                <div class="quick-action-title">查看院系排名</div>
                <div class="quick-action-desc">查看各院系的得分排名</div>
            </a>
        </div>
    </div>

    <!-- 即将到来的比赛和排名 -->
    <div class="row">
        <!-- 即将到来的比赛 -->
        <div class="col-md-6 mb-4">
            <div class="data-section">
                <div class="data-section-title">
                    <h3><i class="bi bi-calendar-event me-2"></i>即将到来的比赛</h3>
                    <a href="{{ url_for('event.index') }}" class="data-section-link">查看全部 <i class="bi bi-arrow-right"></i></a>
                </div>
                <div class="data-card">
                    <div class="table-responsive">
                        <table class="table data-table">
                            <thead>
                                <tr>
                                    <th>项目名称</th>
                                    <th>类型</th>
                                    <th>时间</th>
                                    <th>地点</th>
                                </tr>
                            </thead>
                            <tbody id="upcoming-events-table">
                                <tr>
                                    <td colspan="4" class="text-center">
                                        <div class="loading-animation"></div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 得分最高的运动员 -->
        <div class="col-md-6 mb-4">
            <div class="data-section">
                <div class="data-section-title">
                    <h3><i class="bi bi-trophy me-2"></i>得分最高的运动员</h3>
                    <a href="{{ url_for('athlete.index') }}" class="data-section-link">查看全部 <i class="bi bi-arrow-right"></i></a>
                </div>
                <div class="data-card">
                    <div class="table-responsive">
                        <table class="table data-table">
                            <thead>
                                <tr>
                                    <th>姓名</th>
                                    <th>学号</th>
                                    <th>院系</th>
                                    <th>得分</th>
                                </tr>
                            </thead>
                            <tbody id="top-athletes-table">
                                <tr>
                                    <td colspan="4" class="text-center">
                                        <div class="loading-animation"></div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 院系得分排名 -->
    <div class="row">
        <div class="col-12 mb-4">
            <div class="data-section">
                <div class="data-section-title">
                    <h3><i class="bi bi-bar-chart me-2"></i>院系得分排名</h3>
                    <a href="{{ url_for('score.department_ranking') }}" class="data-section-link">查看详情 <i class="bi bi-arrow-right"></i></a>
                </div>
                <div class="data-card">
                    <div class="table-responsive">
                        <table class="table data-table">
                            <thead>
                                <tr>
                                    <th>排名</th>
                                    <th>院系代码</th>
                                    <th>院系名称</th>
                                    <th>总得分</th>
                                </tr>
                            </thead>
                            <tbody id="department-ranking-table">
                                <tr>
                                    <td colspan="4" class="text-center">
                                        <div class="loading-animation"></div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // 更新当前时间
    function updateCurrentTime() {
        const now = new Date();
        const options = {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        };
        document.getElementById('current-date').textContent = now.toLocaleDateString('zh-CN', options);
    }

    // 每秒更新一次时间
    setInterval(updateCurrentTime, 1000);

    // 加载统计数据
    function loadStatistics() {
        fetch('/api/statistics')
            .then(response => response.json())
            .then(data => {
                document.getElementById('department-count').textContent = data.department_count;
                document.getElementById('athlete-count').textContent = data.athlete_count;
                document.getElementById('event-count').textContent = data.event_count;
                document.getElementById('judge-count').textContent = data.judge_count;
                document.getElementById('score-count').textContent = data.score_count;
                document.getElementById('today-events').textContent = data.today_events;
            })
            .catch(error => console.error('Error loading statistics:', error));
    }

    // 加载即将到来的比赛
    function loadUpcomingEvents() {
        fetch('/api/upcoming_events')
            .then(response => response.json())
            .then(data => {
                const tableBody = document.getElementById('upcoming-events-table');
                tableBody.innerHTML = '';

                if (data.length === 0) {
                    const row = document.createElement('tr');
                    row.innerHTML = '<td colspan="4" class="text-center">暂无即将到来的比赛</td>';
                    tableBody.appendChild(row);
                } else {
                    data.forEach(event => {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td><a href="/event/detail/${event.event_id}">${event.event_name}</a></td>
                            <td>${event.event_type}</td>
                            <td>${event.start_time}</td>
                            <td>${event.location || '-'}</td>
                        `;
                        tableBody.appendChild(row);
                    });
                }
            })
            .catch(error => console.error('Error loading upcoming events:', error));
    }

    // 加载得分最高的运动员
    function loadTopAthletes() {
        fetch('/api/top_athletes')
            .then(response => response.json())
            .then(data => {
                const tableBody = document.getElementById('top-athletes-table');
                tableBody.innerHTML = '';

                if (data.length === 0) {
                    const row = document.createElement('tr');
                    row.innerHTML = '<td colspan="4" class="text-center">暂无运动员得分数据</td>';
                    tableBody.appendChild(row);
                } else {
                    data.forEach(athlete => {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td><a href="/athlete/detail/${athlete.athlete_id}">${athlete.name}</a></td>
                            <td>${athlete.student_id}</td>
                            <td>${athlete.dept_name}</td>
                            <td>${athlete.total_points}</td>
                        `;
                        tableBody.appendChild(row);
                    });
                }
            })
            .catch(error => console.error('Error loading top athletes:', error));
    }

    // 加载院系得分排名
    function loadDepartmentRanking() {
        fetch('/api/department_points')
            .then(response => response.json())
            .then(data => {
                const tableBody = document.getElementById('department-ranking-table');
                tableBody.innerHTML = '';

                if (data.length === 0) {
                    const row = document.createElement('tr');
                    row.innerHTML = '<td colspan="4" class="text-center">暂无院系得分数据</td>';
                    tableBody.appendChild(row);
                } else {
                    data.forEach((department, index) => {
                        const row = document.createElement('tr');
                        let rankClass = '';
                        if (index === 0) rankClass = 'rank-1';
                        else if (index === 1) rankClass = 'rank-2';
                        else if (index === 2) rankClass = 'rank-3';

                        row.innerHTML = `
                            <td><span class="rank-badge ${rankClass}">${index + 1}</span></td>
                            <td>${department.dept_code}</td>
                            <td>${department.dept_name}</td>
                            <td><strong>${department.total_points}</strong></td>
                        `;
                        tableBody.appendChild(row);
                    });
                }
            })
            .catch(error => console.error('Error loading department ranking:', error));
    }

    // 页面加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
        updateCurrentTime();
        loadStatistics();
        loadUpcomingEvents();
        loadTopAthletes();
        loadDepartmentRanking();
    });
</script>
{% endblock %}
