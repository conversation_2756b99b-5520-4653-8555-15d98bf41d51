{% extends "layout.html" %}

{% block title %}编辑成绩 - 运动会管理系统{% endblock %}

{% block page_title %}编辑成绩{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">编辑成绩</h5>
    </div>
    <div class="card-body">
        <form method="post" action="{{ url_for('score.edit', score_id=score.score_id) }}">
            {{ form.csrf_token }}
            
            <div class="mb-3">
                <label for="athlete_id" class="form-label">运动员</label>
                {{ form.athlete_id(class="form-select", id="athlete_id") }}
                <div class="form-text">运动员和比赛项目不可修改</div>
            </div>
            
            <div class="mb-3">
                <label for="event_id" class="form-label">比赛项目</label>
                {{ form.event_id(class="form-select", id="event_id") }}
                <div class="form-text">运动员和比赛项目不可修改</div>
            </div>
            
            <div class="mb-3">
                <label for="result" class="form-label">成绩</label>
                {{ form.result(class="form-control", id="result") }}
                {% if form.result.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.result.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% endif %}
                <div class="form-text">例如：10.5秒、5.2米、2分30秒等</div>
            </div>
            
            <div class="mb-3">
                <label for="ranking" class="form-label">名次</label>
                {{ form.ranking(class="form-control", id="ranking") }}
                {% if form.ranking.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.ranking.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% endif %}
                <div class="form-text">可选，填写数字，例如：1、2、3等</div>
            </div>
            
            <div class="mb-3">
                <label for="points" class="form-label">得分</label>
                {{ form.points(class="form-control", id="points") }}
                {% if form.points.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.points.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% endif %}
                <div class="form-text">可选，填写数字，例如：10、8、6等</div>
            </div>
            
            <div class="d-flex justify-content-between">
                <a href="{{ url_for('score.index') }}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> 返回
                </a>
                {{ form.submit(class="btn btn-primary") }}
            </div>
        </form>
    </div>
</div>
{% endblock %}
