<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 运动会管理系统</title>
    <!-- Bootstrap 5 CSS -->
    <link href="{{ url_for('static', filename='vendor/cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min_cf28afd9.css') }}" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="{{ url_for('static', filename='vendor/cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons_d576b5bc.css') }}" rel="stylesheet">
    <!-- 自定义CSS -->
    <link href="{{ url_for('static', filename='css/main.css') }}" rel="stylesheet">
    <style>
        :root {
            /* 使用与主系统相同的变量 */
            --primary-color: #4759b8;
            --primary-hover: #3949a2;
            --primary-light: #eef0fb;
            --secondary-color: #36cfc9;
            --border-radius: 8px;
            --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
        }

        body {
            font-family: "Microsoft YaHei", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background: linear-gradient(135deg, var(--primary-light) 0%, #ffffff 100%);
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            overflow: hidden;
        }

        .login-wrapper {
            width: 100%;
            max-width: 900px;
            display: flex;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border-radius: var(--border-radius);
            overflow: hidden;
            background-color: #fff;
            animation: fadeIn 0.5s ease;
        }

        .login-banner {
            flex: 1;
            background-color: var(--primary-color);
            padding: 40px;
            color: #fff;
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .login-banner::before {
            content: '';
            position: absolute;
            top: -50px;
            left: -50px;
            width: 200px;
            height: 200px;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
        }

        .login-banner::after {
            content: '';
            position: absolute;
            bottom: -80px;
            right: -80px;
            width: 300px;
            height: 300px;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
        }

        .login-banner h2 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
        }

        .login-banner p {
            font-size: 1.1rem;
            margin-bottom: 30px;
            opacity: 0.9;
            line-height: 1.6;
            position: relative;
            z-index: 1;
        }

        .login-form-container {
            flex: 1;
            padding: 40px;
            background-color: #fff;
        }

        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .login-header i {
            font-size: 3rem;
            color: var(--primary-color);
            margin-bottom: 15px;
            display: inline-block;
        }

        .login-header h1 {
            font-size: 1.8rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 10px;
        }

        .login-header p {
            color: #777;
            font-size: 1rem;
        }

        .form-floating {
            margin-bottom: 20px;
        }

        .form-control {
            border-radius: var(--border-radius);
            padding: 12px 15px;
            height: calc(3.5rem + 2px);
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.25rem rgba(71, 89, 184, 0.25);
        }

        .form-floating > label {
            padding: 1rem 1.25rem;
        }

        .btn-login {
            width: 100%;
            padding: 12px;
            font-size: 1rem;
            font-weight: 500;
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            border-radius: var(--border-radius);
            transition: all 0.3s ease;
        }

        .btn-login:hover {
            background-color: var(--primary-hover);
            border-color: var(--primary-hover);
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(71, 89, 184, 0.3);
        }

        .login-footer {
            text-align: center;
            margin-top: 30px;
            color: #777;
            font-size: 0.9rem;
        }

        .invalid-feedback {
            font-size: 0.85rem;
            color: #dc3545;
            margin-top: 5px;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @media (max-width: 768px) {
            .login-wrapper {
                flex-direction: column;
                max-width: 400px;
            }

            .login-banner {
                padding: 30px;
                text-align: center;
            }

            .login-banner h2 {
                font-size: 2rem;
            }

            .login-form-container {
                padding: 30px;
            }
        }
    </style>
</head>
<body>
    <div class="login-wrapper">
        <!-- 左侧横幅 -->
        <div class="login-banner">
            <h2>运动会管理系统</h2>
            <p>欢迎使用运动会管理系统，这是一个专为学校运动会设计的管理平台。通过本系统，您可以轻松管理院系、运动员、比赛项目、裁判员和比赛成绩等信息。</p>
        </div>

        <!-- 右侧登录表单 -->
        <div class="login-form-container">
            <!-- 消息提示 -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <div class="login-header">
                <i class="bi bi-trophy"></i>
                <h1>用户登录</h1>
                <p>请输入您的账号信息以登录系统</p>
            </div>

            <form method="post" action="{{ url_for('auth.login') }}">
                {{ form.csrf_token }}

                <div class="form-floating">
                    {{ form.username(class="form-control", id="username", placeholder="用户名") }}
                    <label for="username">用户名</label>
                    {% if form.username.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.username.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <div class="form-floating">
                    {{ form.password(class="form-control", id="password", placeholder="密码") }}
                    <label for="password">密码</label>
                    {% if form.password.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.password.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <div class="d-grid gap-2 mt-4">
                    {{ form.submit(class="btn btn-primary btn-login") }}
                </div>
            </form>

            <div class="login-footer">
                <p>默认管理员账号: admin / 密码: admin</p>
                <p>© {{ now.year }} 运动会管理系统 | 版权所有</p>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="{{ url_for('static', filename='vendor/cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min_6b7a626f.js') }}"></script>
    <script>
        // 添加表单提交动画
        document.querySelector('form').addEventListener('submit', function() {
            const button = this.querySelector('.btn-login');
            button.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span> 登录中...';
            button.disabled = true;
        });

        // 输入框动画效果
        document.querySelectorAll('.form-control').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.classList.add('focused');
            });

            input.addEventListener('blur', function() {
                this.parentElement.classList.remove('focused');
            });
        });
    </script>
</body>
</html>
