{% extends "layout.html" %}

{% block title %}添加学生到比赛项目 - 运动会管理系统{% endblock %}

{% block page_title %}添加学生到比赛项目{% endblock %}

{% block content %}
<div class="card shadow-sm">
    <div class="card-header bg-white py-3">
        <h5 class="mb-0 text-primary">
            <i class="bi bi-person-plus me-2"></i>添加学生到比赛项目
        </h5>
    </div>
    <div class="card-body">
        <form method="post" action="{{ url_for('event_athlete.add') }}">
            {{ form.csrf_token }}
            
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="event_id" class="form-label">比赛项目</label>
                        {{ form.event_id(class="form-select", id="event_id") }}
                        {% if form.event_id.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.event_id.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="athlete_id" class="form-label">运动员</label>
                        {{ form.athlete_id(class="form-select", id="athlete_id") }}
                        {% if form.athlete_id.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.athlete_id.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="alert alert-info mb-4">
                <div class="d-flex">
                    <div class="me-3">
                        <i class="bi bi-info-circle-fill" style="font-size: 1.5rem;"></i>
                    </div>
                    <div>
                        <h5 class="alert-heading">提示</h5>
                        <p class="mb-0">以下成绩信息可以稍后填写。您也可以在学生列表页面直接编辑成绩。</p>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="result" class="form-label">成绩</label>
                        {{ form.result(class="form-control", id="result", placeholder="例如: 10.5秒, 5.2米") }}
                        {% if form.result.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.result.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">可选，填写具体成绩</div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="ranking" class="form-label">排名</label>
                        {{ form.ranking(class="form-control", id="ranking", placeholder="例如: 1, 2, 3") }}
                        {% if form.ranking.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.ranking.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">可选，填写排名</div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="points" class="form-label">得分</label>
                        {{ form.points(class="form-control", id="points", placeholder="例如: 5, 3, 1") }}
                        {% if form.points.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.points.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">可选，填写得分</div>
                    </div>
                </div>
            </div>
            
            <div class="d-flex justify-content-between mt-4">
                <a href="{{ url_for('event_athlete.index', event_id=request.args.get('event_id', '')) }}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> 返回
                </a>
                {{ form.submit(class="btn btn-primary") }}
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // 初始化Select2
        $('#event_id, #athlete_id').select2({
            theme: 'bootstrap-5',
            width: '100%'
        });
        
        // 当比赛项目改变时，更新运动员列表
        $('#event_id').on('change', function() {
            const eventId = $(this).val();
            
            if (!eventId) return;
            
            // 获取符合条件的运动员
            $.ajax({
                url: "{{ url_for('event_athlete.api_athletes') }}",
                type: 'GET',
                data: { event_id: eventId },
                success: function(athletes) {
                    // 清空运动员选择框
                    $('#athlete_id').empty();
                    
                    // 添加运动员选项
                    $.each(athletes, function(index, athlete) {
                        $('#athlete_id').append(new Option(
                            `${athlete.name} (${athlete.student_id} - ${athlete.dept_name})`, 
                            athlete.athlete_id
                        ));
                    });
                    
                    // 触发change事件，更新Select2
                    $('#athlete_id').trigger('change');
                },
                error: function(xhr) {
                    console.error('获取运动员失败:', xhr.responseText);
                }
            });
        });
    });
</script>
{% endblock %}
