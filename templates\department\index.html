{% extends "layout.html" %}

{% block title %}院系管理 - 运动会管理系统{% endblock %}

{% block page_title %}院系管理{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">院系列表</h5>
        {% if session.role == 'admin' %}
        <a href="{{ url_for('department.add') }}" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> 添加院系
        </a>
        {% endif %}
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover datatable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>院系代码</th>
                        <th>院系名称</th>
                        <th>运动员数量</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="department-table">
                    <tr>
                        <td colspan="5" class="text-center">加载中...</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 加载院系数据
        loadDepartmentData();
    });

    // 加载院系数据
    function loadDepartmentData() {
        fetch('/department/api/list')
            .then(response => response.json())
            .then(data => {
                const tableBody = document.getElementById('department-table');
                tableBody.innerHTML = '';

                if (data.length === 0) {
                    const row = document.createElement('tr');
                    row.innerHTML = '<td colspan="5" class="text-center">暂无院系数据</td>';
                    tableBody.appendChild(row);
                } else {
                    data.forEach(department => {
                        const row = document.createElement('tr');

                        // 构建操作按钮
                        let actions = `<a href="{{ url_for('department.edit', dept_id=0) }}${department.dept_id}" class="btn btn-sm btn-primary me-1"><i class="bi bi-pencil"></i> 编辑</a>`;

                        if (department.athlete_count === 0) {
                            actions += `
                                <form method="POST" action="{{ url_for('department.delete', dept_id=0) }}${department.dept_id}" class="d-inline">
                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                    <button type="submit" class="btn btn-sm btn-danger delete-confirm"><i class="bi bi-trash"></i> 删除</button>
                                </form>
                            `;
                        } else {
                            actions += `<button class="btn btn-sm btn-danger" disabled title="有关联的运动员，无法删除"><i class="bi bi-trash"></i> 删除</button>`;
                        }

                        row.innerHTML = `
                            <td>${department.dept_id}</td>
                            <td>${department.dept_code}</td>
                            <td>${department.dept_name}</td>
                            <td>${department.athlete_count}</td>
                            <td>${actions}</td>
                        `;

                        tableBody.appendChild(row);
                    });

                    // 初始化删除确认
                    document.querySelectorAll('.delete-confirm').forEach(function(button) {
                        button.addEventListener('click', function(e) {
                            if (!confirm('确定要删除该院系吗？此操作不可撤销！')) {
                                e.preventDefault();
                            }
                        });
                    });

                    // 初始化DataTable
                    if ($.fn.DataTable.isDataTable('.datatable')) {
                        $('.datatable').DataTable().destroy();
                    }

                    $('.datatable').DataTable({
                        language: {
                            url: '/static/js/dataTables.chinese.json'
                        },
                        responsive: true,
                        pageLength: 10,
                        lengthMenu: [[10, 25, 50, -1], [10, 25, 50, "全部"]]
                    });
                }
            })
            .catch(error => {
                console.error('Error loading department data:', error);
                const tableBody = document.getElementById('department-table');
                tableBody.innerHTML = '<tr><td colspan="5" class="text-center text-danger">加载数据失败</td></tr>';
            });
    }
</script>
{% endblock %}
