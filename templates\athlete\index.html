{% extends "layout.html" %}

{% block title %}运动员管理 - 运动会管理系统{% endblock %}

{% block page_title %}运动员管理{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">运动员列表</h5>
        {% if session.role == 'admin' %}
        <a href="{{ url_for('athlete.add') }}" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> 添加运动员
        </a>
        {% endif %}
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover datatable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>姓名</th>
                        <th>性别</th>
                        <th>学号</th>
                        <th>所属院系</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="athlete-table">
                    <tr>
                        <td colspan="6" class="text-center">加载中...</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 加载运动员数据
        loadAthleteData();
    });

    // 加载运动员数据
    function loadAthleteData() {
        // 显示加载中
        const tableBody = document.getElementById('athlete-table');
        tableBody.innerHTML = '<tr><td colspan="6" class="text-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">加载中...</span></div></td></tr>';

        // 获取数据
        fetch('/athlete/api/list')
            .then(response => response.json())
            .then(data => {
                // 清空表格
                tableBody.innerHTML = '';

                if (data.length === 0) {
                    // 如果没有数据，显示提示信息
                    tableBody.innerHTML = '<tr><td colspan="6" class="text-center">暂无运动员数据</td></tr>';
                } else {
                    // 添加数据行
                    data.forEach(athlete => {
                        const row = document.createElement('tr');

                        // 构建操作按钮
                        let actions = `
                            <a href="{{ url_for('athlete.detail', athlete_id=0) }}${athlete.athlete_id}" class="btn btn-sm btn-info me-1"><i class="bi bi-eye"></i> 详情</a>
                            <a href="{{ url_for('athlete.edit', athlete_id=0) }}${athlete.athlete_id}" class="btn btn-sm btn-primary me-1"><i class="bi bi-pencil"></i> 编辑</a>
                        `;

                        // 只有没有成绩记录的运动员才能删除
                        if (athlete.scores && athlete.scores.length === 0) {
                            actions += `
                                <form method="POST" action="{{ url_for('athlete.delete', athlete_id=0) }}${athlete.athlete_id}" class="d-inline">
                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                    <button type="submit" class="btn btn-sm btn-danger delete-confirm"><i class="bi bi-trash"></i> 删除</button>
                                </form>
                            `;
                        } else {
                            actions += `<button class="btn btn-sm btn-danger" disabled title="有关联的成绩记录，无法删除"><i class="bi bi-trash"></i> 删除</button>`;
                        }

                        // 设置行内容
                        row.innerHTML = `
                            <td>${athlete.athlete_id}</td>
                            <td>${athlete.name}</td>
                            <td>${athlete.gender}</td>
                            <td>${athlete.student_id}</td>
                            <td>${athlete.dept_name} (${athlete.dept_code})</td>
                            <td>${actions}</td>
                        `;

                        tableBody.appendChild(row);
                    });

                    // 初始化删除确认
                    document.querySelectorAll('.delete-confirm').forEach(function(button) {
                        button.addEventListener('click', function(e) {
                            if (!confirm('确定要删除该运动员吗？此操作不可撤销！')) {
                                e.preventDefault();
                            }
                        });
                    });

                    // 初始化DataTable
                    if ($.fn.DataTable.isDataTable('.datatable')) {
                        $('.datatable').DataTable().destroy();
                    }

                    $('.datatable').DataTable({
                        language: {
                            url: '/static/js/dataTables.chinese.json'
                        },
                        responsive: true,
                        pageLength: 10,
                        lengthMenu: [[10, 25, 50, -1], [10, 25, 50, "全部"]]
                    });
                }
            })
            .catch(error => {
                console.error('Error loading athlete data:', error);
                tableBody.innerHTML = '<tr><td colspan="6" class="text-center text-danger">加载数据失败</td></tr>';
            });
    }
</script>
{% endblock %}
