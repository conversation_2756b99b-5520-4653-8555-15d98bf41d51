{% extends "layout.html" %}

{% block title %}比赛项目管理 - 运动会管理系统{% endblock %}

{% block page_title %}比赛项目管理{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">比赛项目列表</h5>
        {% if session.role == 'admin' %}
        <a href="{{ url_for('event.add') }}" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> 添加比赛项目
        </a>
        {% endif %}
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover datatable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>项目名称</th>
                        <th>项目类型</th>
                        <th>性别限制</th>
                        <th>开始时间</th>
                        <th>比赛地点</th>
                        <th>裁判数量</th>
                        <th>成绩数量</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="event-table">
                    <tr>
                        <td colspan="9" class="text-center">加载中...</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 加载比赛项目数据
        loadEventData();
    });

    // 加载比赛项目数据
    function loadEventData() {
        fetch('/event/api/list')
            .then(response => response.json())
            .then(data => {
                const tableBody = document.getElementById('event-table');
                tableBody.innerHTML = '';

                if (data.length === 0) {
                    const row = document.createElement('tr');
                    row.innerHTML = '<td colspan="9" class="text-center">暂无比赛项目数据</td>';
                    tableBody.appendChild(row);
                } else {
                    data.forEach(event => {
                        const row = document.createElement('tr');

                        // 构建操作按钮
                        let actions = `
                            <a href="{{ url_for('event.detail', event_id=0) }}${event.event_id}" class="btn btn-sm btn-info me-1"><i class="bi bi-eye"></i> 详情</a>
                            <a href="{{ url_for('event.edit', event_id=0) }}${event.event_id}" class="btn btn-sm btn-primary me-1"><i class="bi bi-pencil"></i> 编辑</a>
                        `;

                        // 只有没有成绩记录的比赛项目才能删除
                        if (event.score_count === 0) {
                            actions += `
                                <form method="POST" action="{{ url_for('event.delete', event_id=0) }}${event.event_id}" class="d-inline">
                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                    <button type="submit" class="btn btn-sm btn-danger delete-confirm"><i class="bi bi-trash"></i> 删除</button>
                                </form>
                            `;
                        } else {
                            actions += `<button class="btn btn-sm btn-danger" disabled title="有关联的成绩记录，无法删除"><i class="bi bi-trash"></i> 删除</button>`;
                        }

                        // 性别限制标签颜色
                        let genderBadge = '';
                        if (event.gender_limit === '男') {
                            genderBadge = '<span class="badge bg-primary">男</span>';
                        } else if (event.gender_limit === '女') {
                            genderBadge = '<span class="badge bg-danger">女</span>';
                        } else {
                            genderBadge = '<span class="badge bg-secondary">不限</span>';
                        }

                        row.innerHTML = `
                            <td>${event.event_id}</td>
                            <td>${event.event_name}</td>
                            <td>${event.event_type}</td>
                            <td>${genderBadge}</td>
                            <td>${event.start_time || '-'}</td>
                            <td>${event.location || '-'}</td>
                            <td>${event.judge_count}</td>
                            <td>${event.score_count}</td>
                            <td>${actions}</td>
                        `;

                        tableBody.appendChild(row);
                    });

                    // 初始化删除确认
                    document.querySelectorAll('.delete-confirm').forEach(function(button) {
                        button.addEventListener('click', function(e) {
                            if (!confirm('确定要删除该比赛项目吗？此操作不可撤销！')) {
                                e.preventDefault();
                            }
                        });
                    });

                    // 初始化DataTable
                    if ($.fn.DataTable.isDataTable('.datatable')) {
                        $('.datatable').DataTable().destroy();
                    }

                    $('.datatable').DataTable({
                        language: {
                            url: '/static/js/dataTables.chinese.json'
                        },
                        responsive: true,
                        pageLength: 10,
                        lengthMenu: [[10, 25, 50, -1], [10, 25, 50, "全部"]]
                    });
                }
            })
            .catch(error => {
                console.error('Error loading event data:', error);
                const tableBody = document.getElementById('event-table');
                tableBody.innerHTML = '<tr><td colspan="9" class="text-center text-danger">加载数据失败</td></tr>';
            });
    }
</script>
{% endblock %}
