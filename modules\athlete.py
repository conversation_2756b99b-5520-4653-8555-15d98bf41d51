"""
运动员管理模块
"""
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_wtf import FlaskForm
from wtforms import StringField, SelectField, SubmitField
from wtforms.validators import DataRequired, Length
from modules.database import db
from modules.models import Athlete, Department, Score
from modules.auth import login_required, admin_required

# 创建运动员蓝图
athlete_bp = Blueprint('athlete', __name__)

# 运动员表单类
class AthleteForm(FlaskForm):
    """运动员表单类，用于添加和编辑运动员信息"""
    name = StringField('姓名', validators=[DataRequired(), Length(max=50)])
    gender = SelectField('性别', choices=[('男', '男'), ('女', '女')], validators=[DataRequired()])
    student_id = StringField('学号', validators=[DataRequired(), Length(max=20)])
    dept_id = SelectField('所属院系', coerce=int, validators=[DataRequired()])
    submit = SubmitField('提交')

    def __init__(self, *args, **kwargs):
        super(AthleteForm, self).__init__(*args, **kwargs)
        self.dept_id.choices = [(dept.dept_id, f"{dept.dept_code} - {dept.dept_name}") for dept in Department.query.all()]

@athlete_bp.route('/')
@login_required
def index():
    """
    运动员列表页面

    Returns:
        运动员列表页面
    """
    # 使用标准表格组件
    return render_template('athlete/index.html')

@athlete_bp.route('/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add():
    """
    添加运动员

    Returns:
        添加运动员页面或重定向到运动员列表页面
    """
    # 创建表单
    form = AthleteForm()

    # 处理表单提交
    if form.validate_on_submit():
        # 获取表单数据
        name = form.name.data
        gender = form.gender.data
        student_id = form.student_id.data
        dept_id = form.dept_id.data

        # 检查学号是否已存在
        existing_athlete = Athlete.query.filter_by(student_id=student_id).first()
        if existing_athlete:
            flash('学号已存在！', 'danger')
            return render_template('athlete/add.html', form=form)

        # 创建运动员
        athlete = Athlete(name=name, gender=gender, student_id=student_id, dept_id=dept_id)

        # 保存到数据库
        db.session.add(athlete)
        db.session.commit()

        # 重定向到运动员列表页面
        flash('运动员添加成功！', 'success')
        return redirect(url_for('athlete.index'))

    # 渲染添加运动员页面
    return render_template('athlete/add.html', form=form)

@athlete_bp.route('/edit/<int:athlete_id>', methods=['GET', 'POST'])
@login_required
@admin_required
def edit(athlete_id):
    """
    编辑运动员

    Args:
        athlete_id: 运动员ID

    Returns:
        编辑运动员页面或重定向到运动员列表页面
    """
    # 查询运动员
    athlete = Athlete.query.get_or_404(athlete_id)

    # 创建表单
    form = AthleteForm(obj=athlete)

    # 处理表单提交
    if form.validate_on_submit():
        # 获取表单数据
        name = form.name.data
        gender = form.gender.data
        student_id = form.student_id.data
        dept_id = form.dept_id.data

        # 检查学号是否已存在（排除当前运动员）
        existing_athlete = Athlete.query.filter(Athlete.student_id == student_id, Athlete.athlete_id != athlete_id).first()
        if existing_athlete:
            flash('学号已存在！', 'danger')
            return render_template('athlete/edit.html', form=form, athlete=athlete)

        # 更新运动员
        athlete.name = name
        athlete.gender = gender
        athlete.student_id = student_id
        athlete.dept_id = dept_id

        # 保存到数据库
        db.session.commit()

        # 重定向到运动员列表页面
        flash('运动员更新成功！', 'success')
        return redirect(url_for('athlete.index'))

    # 渲染编辑运动员页面
    return render_template('athlete/edit.html', form=form, athlete=athlete)

@athlete_bp.route('/delete/<int:athlete_id>', methods=['POST'])
@login_required
@admin_required
def delete(athlete_id):
    """
    删除运动员

    Args:
        athlete_id: 运动员ID

    Returns:
        重定向到运动员列表页面
    """
    # 查询运动员
    athlete = Athlete.query.get_or_404(athlete_id)

    # 检查是否有关联的成绩
    if athlete.scores:
        flash('无法删除运动员，因为有关联的成绩记录！', 'danger')
        return redirect(url_for('athlete.index'))

    # 删除运动员
    db.session.delete(athlete)
    db.session.commit()

    # 重定向到运动员列表页面
    flash('运动员删除成功！', 'success')
    return redirect(url_for('athlete.index'))

@athlete_bp.route('/detail/<int:athlete_id>')
@login_required
def detail(athlete_id):
    """
    运动员详情页面

    Args:
        athlete_id: 运动员ID

    Returns:
        运动员详情页面
    """
    # 查询运动员
    athlete = Athlete.query.get_or_404(athlete_id)

    # 查询运动员的成绩
    scores = Score.query.filter_by(athlete_id=athlete_id).all()

    return render_template('athlete/detail.html', athlete=athlete, scores=scores)

@athlete_bp.route('/api/list')
@login_required
def api_list():
    """
    运动员列表API
    用于AJAX请求获取运动员列表

    Returns:
        JSON格式的运动员列表
    """
    athletes = Athlete.query.all()
    result = []

    for athlete in athletes:
        # 查询运动员的成绩
        scores = Score.query.filter_by(athlete_id=athlete.athlete_id).all()

        result.append({
            'athlete_id': athlete.athlete_id,
            'name': athlete.name,
            'gender': athlete.gender,
            'student_id': athlete.student_id,
            'dept_id': athlete.dept_id,
            'dept_name': athlete.department.dept_name,
            'dept_code': athlete.department.dept_code,
            'scores': [{
                'score_id': score.score_id,
                'event_id': score.event_id,
                'result': score.result,
                'ranking': score.ranking,
                'points': float(score.points) if score.points else 0
            } for score in scores]
        })

    return jsonify(result)
