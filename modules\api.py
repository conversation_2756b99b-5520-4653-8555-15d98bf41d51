"""
API模块
"""
from flask import Blueprint, jsonify, request
from modules.models import Department, Athlete, Event, Judge, Score
from datetime import datetime, timedelta
from modules.database import db
from sqlalchemy import func, desc
from modules.auth import login_required

# 创建API蓝图
api_bp = Blueprint('api', __name__, url_prefix='/api')

@api_bp.route('/statistics', methods=['GET'])
@login_required
def get_statistics():
    """获取系统概览统计数据"""
    # 获取院系总数
    total_departments = db.session.query(Department).count()

    # 获取运动员总数
    total_athletes = db.session.query(Athlete).count()

    # 获取比赛项目总数
    total_events = db.session.query(Event).count()

    # 获取裁判员总数
    total_judges = db.session.query(Judge).count()

    # 获取成绩记录总数
    total_scores = db.session.query(Score).count()

    # 获取今日比赛数量
    today = datetime.now().date()
    today_events = db.session.query(Event).filter(
        func.date(Event.start_time) == today
    ).count()

    # 获取男女运动员比例
    male_count = db.session.query(Athlete).filter(Athlete.gender == '男').count()
    female_count = db.session.query(Athlete).filter(Athlete.gender == '女').count()

    # 获取各类型比赛项目数量
    event_types = db.session.query(
        Event.event_type,
        func.count(Event.event_id).label('count')
    ).group_by(Event.event_type).all()

    event_type_stats = {event_type: count for event_type, count in event_types}

    return jsonify({
        'department_count': total_departments,
        'athlete_count': total_athletes,
        'event_count': total_events,
        'judge_count': total_judges,
        'score_count': total_scores,
        'today_events': today_events,
        'gender_ratio': {
            'male': male_count,
            'female': female_count
        },
        'event_types': event_type_stats
    })

@api_bp.route('/upcoming_events', methods=['GET'])
@login_required
def get_upcoming_events():
    """获取即将到来的比赛项目"""
    now = datetime.now()
    upcoming_events = db.session.query(Event).filter(
        Event.start_time > now
    ).order_by(Event.start_time).limit(5).all()

    result = [{
        'event_id': event.event_id,
        'event_name': event.event_name,
        'event_type': event.event_type,
        'gender_limit': event.gender_limit,
        'start_time': event.start_time.strftime('%Y-%m-%d %H:%M') if event.start_time else '',
        'location': event.location
    } for event in upcoming_events]

    return jsonify(result)

@api_bp.route('/top_athletes', methods=['GET'])
@login_required
def get_top_athletes():
    """获取得分最高的运动员"""
    top_athletes = db.session.query(
        Athlete,
        func.sum(Score.points).label('total_points')
    ).join(Score, Score.athlete_id == Athlete.athlete_id) \
     .group_by(Athlete.athlete_id) \
     .order_by(func.sum(Score.points).desc()) \
     .limit(10) \
     .all()

    result = [{
        'athlete_id': athlete.athlete_id,
        'name': athlete.name,
        'gender': athlete.gender,
        'student_id': athlete.student_id,
        'dept_name': athlete.department.dept_name,
        'total_points': float(total_points) if total_points else 0
    } for athlete, total_points in top_athletes]

    return jsonify(result)

@api_bp.route('/department_points', methods=['GET'])
@login_required
def get_department_points():
    """获取各院系得分情况"""
    department_points = db.session.query(
        Department,
        func.sum(Score.points).label('total_points')
    ).join(Athlete, Athlete.dept_id == Department.dept_id) \
     .join(Score, Score.athlete_id == Athlete.athlete_id) \
     .group_by(Department.dept_id) \
     .order_by(func.sum(Score.points).desc()) \
     .all()

    result = [{
        'dept_id': department.dept_id,
        'dept_name': department.dept_name,
        'dept_code': department.dept_code,
        'total_points': float(total_points) if total_points else 0
    } for department, total_points in department_points]

    return jsonify(result)
