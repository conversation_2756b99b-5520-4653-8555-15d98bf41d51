{% extends "layout.html" %}

{% block title %}比赛项目详情 - 运动会管理系统{% endblock %}

{% block page_title %}比赛项目详情{% endblock %}

{% block content %}
<div class="row">
    <!-- 比赛项目信息卡片 -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="bi bi-calendar-event"></i> 基本信息</h5>
            </div>
            <div class="card-body">
                <h4 class="text-center mb-4">{{ event.event_name }}</h4>
                
                <ul class="list-group list-group-flush">
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span><i class="bi bi-tag me-2"></i> 项目类型</span>
                        <span class="badge bg-info rounded-pill">{{ event.event_type }}</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span><i class="bi bi-gender-ambiguous me-2"></i> 性别限制</span>
                        <span class="badge bg-{% if event.gender_limit == '男' %}primary{% elif event.gender_limit == '女' %}danger{% else %}secondary{% endif %} rounded-pill">{{ event.gender_limit }}</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span><i class="bi bi-people me-2"></i> 最大参与人数</span>
                        <span>{{ event.max_participants or '不限' }}</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span><i class="bi bi-clock me-2"></i> 开始时间</span>
                        <span>{{ event.start_time.strftime('%Y-%m-%d %H:%M') if event.start_time else '未设置' }}</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span><i class="bi bi-geo-alt me-2"></i> 比赛地点</span>
                        <span>{{ event.location or '未设置' }}</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span><i class="bi bi-flag me-2"></i> 裁判数量</span>
                        <span class="badge bg-warning rounded-pill">{{ judges|length }}</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span><i class="bi bi-clipboard-data me-2"></i> 成绩记录数量</span>
                        <span class="badge bg-success rounded-pill">{{ scores|length }}</span>
                    </li>
                </ul>
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between">
                    <a href="{{ url_for('event.index') }}" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i> 返回
                    </a>
                    <a href="{{ url_for('event.edit', event_id=event.event_id) }}" class="btn btn-primary">
                        <i class="bi bi-pencil"></i> 编辑
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 裁判列表卡片 -->
    <div class="col-md-8 mb-4">
        <div class="card">
            <div class="card-header bg-warning text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="bi bi-flag"></i> 裁判列表</h5>
                {% if session.role == 'admin' %}
                <a href="{{ url_for('event.add_judge', event_id=event.event_id) }}" class="btn btn-sm btn-light">
                    <i class="bi bi-plus-circle"></i> 添加裁判
                </a>
                {% endif %}
            </div>
            <div class="card-body">
                {% if judges %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>姓名</th>
                                <th>级别</th>
                                <th>联系方式</th>
                                <th>角色</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for judge_info in judges %}
                            <tr>
                                <td>{{ judge_info.judge.name }}</td>
                                <td>{{ judge_info.judge.level or '-' }}</td>
                                <td>{{ judge_info.judge.contact or '-' }}</td>
                                <td>{{ judge_info.role }}</td>
                                <td>
                                    {% if session.role == 'admin' %}
                                    <form method="POST" action="{{ url_for('event.remove_judge', event_id=event.event_id, judge_id=judge_info.judge.judge_id) }}" class="d-inline">
                                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                        <button type="submit" class="btn btn-sm btn-danger delete-confirm">
                                            <i class="bi bi-trash"></i> 移除
                                        </button>
                                    </form>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i> 该比赛项目暂无裁判
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 成绩列表卡片 -->
<div class="card mb-4">
    <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
        <h5 class="mb-0"><i class="bi bi-clipboard-data"></i> 成绩列表</h5>
        {% if session.role == 'admin' %}
        <a href="{{ url_for('score.add') }}" class="btn btn-sm btn-light">
            <i class="bi bi-plus-circle"></i> 添加成绩
        </a>
        {% endif %}
    </div>
    <div class="card-body">
        {% if scores %}
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>运动员</th>
                        <th>学号</th>
                        <th>院系</th>
                        <th>成绩</th>
                        <th>名次</th>
                        <th>得分</th>
                        <th>记录时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for score in scores %}
                    <tr>
                        <td>
                            <a href="{{ url_for('athlete.detail', athlete_id=score.athlete.athlete_id) }}">
                                {{ score.athlete.name }}
                            </a>
                        </td>
                        <td>{{ score.athlete.student_id }}</td>
                        <td>{{ score.athlete.department.dept_name }}</td>
                        <td>{{ score.result }}</td>
                        <td>
                            {% if score.ranking %}
                                {% if score.ranking == 1 %}
                                    <span class="badge bg-warning">第{{ score.ranking }}名</span>
                                {% elif score.ranking == 2 %}
                                    <span class="badge bg-secondary">第{{ score.ranking }}名</span>
                                {% elif score.ranking == 3 %}
                                    <span class="badge bg-danger">第{{ score.ranking }}名</span>
                                {% else %}
                                    <span class="badge bg-light text-dark">第{{ score.ranking }}名</span>
                                {% endif %}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td>{{ score.points or 0 }}</td>
                        <td>{{ score.record_time.strftime('%Y-%m-%d %H:%M') }}</td>
                        <td>
                            {% if session.role == 'admin' %}
                            <a href="{{ url_for('score.edit', score_id=score.score_id) }}" class="btn btn-sm btn-primary me-1">
                                <i class="bi bi-pencil"></i> 编辑
                            </a>
                            <form method="POST" action="{{ url_for('score.delete', score_id=score.score_id) }}" class="d-inline">
                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                <button type="submit" class="btn btn-sm btn-danger delete-confirm">
                                    <i class="bi bi-trash"></i> 删除
                                </button>
                            </form>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="alert alert-info">
            <i class="bi bi-info-circle"></i> 该比赛项目暂无成绩记录
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化删除确认
        document.querySelectorAll('.delete-confirm').forEach(function(button) {
            button.addEventListener('click', function(e) {
                if (!confirm('确定要执行此操作吗？此操作不可撤销！')) {
                    e.preventDefault();
                }
            });
        });
    });
</script>
{% endblock %}
