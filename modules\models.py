"""
数据模型定义模块
"""
from datetime import datetime
from modules.database import db
from flask_login import UserMixin

# 院系模型
class Department(db.Model):
    """院系表"""
    __tablename__ = 'department'
    
    dept_id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    dept_name = db.Column(db.String(50), nullable=False)
    dept_code = db.Column(db.String(10), nullable=False, unique=True)
    
    # 关联运动员
    athletes = db.relationship('Athlete', backref='department', lazy=True)
    
    def __repr__(self):
        return f'<Department {self.dept_name}>'

# 运动员模型
class Athlete(db.Model):
    """运动员表"""
    __tablename__ = 'athlete'
    
    athlete_id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    name = db.Column(db.String(50), nullable=False)
    gender = db.Column(db.<PERSON>um('男', '女'), nullable=False)
    student_id = db.Column(db.String(20), nullable=False, unique=True)
    dept_id = db.Column(db.Integer, db.ForeignKey('department.dept_id'), nullable=False)
    
    # 关联成绩
    scores = db.relationship('Score', backref='athlete', lazy=True)
    
    def __repr__(self):
        return f'<Athlete {self.name}>'

# 比赛项目模型
class Event(db.Model):
    """比赛项目表"""
    __tablename__ = 'event'
    
    event_id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    event_name = db.Column(db.String(100), nullable=False)
    event_type = db.Column(db.Enum('田赛', '径赛', '球类', '游泳'), nullable=False)
    gender_limit = db.Column(db.Enum('男', '女', '不限'), default='不限')
    max_participants = db.Column(db.Integer)
    start_time = db.Column(db.DateTime)
    location = db.Column(db.String(50))
    
    # 关联成绩和裁判
    scores = db.relationship('Score', backref='event', lazy=True)
    judges = db.relationship('Judge', secondary='event_judge', backref=db.backref('events', lazy='dynamic'))
    
    def __repr__(self):
        return f'<Event {self.event_name}>'

# 裁判员模型
class Judge(db.Model):
    """裁判员表"""
    __tablename__ = 'judge'
    
    judge_id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    name = db.Column(db.String(50), nullable=False)
    level = db.Column(db.String(20))
    contact = db.Column(db.String(20))
    
    def __repr__(self):
        return f'<Judge {self.name}>'

# 项目裁判关联模型
class EventJudge(db.Model):
    """项目裁判关联表"""
    __tablename__ = 'event_judge'
    
    event_id = db.Column(db.Integer, db.ForeignKey('event.event_id'), primary_key=True)
    judge_id = db.Column(db.Integer, db.ForeignKey('judge.judge_id'), primary_key=True)
    role = db.Column(db.String(20))
    
    def __repr__(self):
        return f'<EventJudge {self.event_id}-{self.judge_id}>'

# 成绩模型
class Score(db.Model):
    """成绩表"""
    __tablename__ = 'score'
    
    score_id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    athlete_id = db.Column(db.Integer, db.ForeignKey('athlete.athlete_id'), nullable=False)
    event_id = db.Column(db.Integer, db.ForeignKey('event.event_id'), nullable=False)
    result = db.Column(db.String(20))
    ranking = db.Column(db.Integer)
    points = db.Column(db.Integer)
    record_time = db.Column(db.TIMESTAMP, default=datetime.now)
    
    __table_args__ = (db.UniqueConstraint('athlete_id', 'event_id', name='uix_athlete_event'),)
    
    def __repr__(self):
        return f'<Score {self.score_id}>'

# 用户模型
class User(db.Model, UserMixin):
    """用户表"""
    __tablename__ = 'users'
    
    username = db.Column(db.String(20), primary_key=True)
    password = db.Column(db.String(100), nullable=False)
    role = db.Column(db.String(20), default='user')
    
    # 实现Flask-Login必需的属性和方法
    def get_id(self):
        return self.username
    
    @property
    def is_authenticated(self):
        return True
    
    @property
    def is_active(self):
        return True
    
    @property
    def is_anonymous(self):
        return False
    
    def __repr__(self):
        return f'<User {self.username}>'
