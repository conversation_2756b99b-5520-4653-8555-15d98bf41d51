<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}运动会管理系统{% endblock %}</title>
    <!-- Bootstrap 5 CSS -->
    <link href="{{ url_for('static', filename='vendor/cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min_cf28afd9.css') }}" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="{{ url_for('static', filename='vendor/cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons_d576b5bc.css') }}" rel="stylesheet">
    <!-- DataTables CSS -->
    <link href="{{ url_for('static', filename='css/dataTables.bootstrap5.min.css') }}" rel="stylesheet">
    <!-- Select2 CSS -->
    <link href="{{ url_for('static', filename='vendor/select2/select2.min.css') }}" rel="stylesheet" />
    <link href="{{ url_for('static', filename='vendor/select2/select2-bootstrap-5-theme.min.css') }}" rel="stylesheet" />
    <!-- 自定义CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/main.css') }}" rel="stylesheet">
    <!-- CSRF令牌 -->
    <meta name="csrf-token" content="{{ csrf_token() }}">
    {% block styles %}{% endblock %}

    <!-- 禁用source map请求，提高加载速度 -->
    <script>
        // 禁用source map请求
        const originalFetch = window.fetch;
        window.fetch = function(url, options) {
            if (typeof url === 'string' && url.endsWith('.map')) {
                return Promise.resolve(new Response('', {status: 200}));
            }
            return originalFetch(url, options);
        };

        // 禁用XMLHttpRequest对source map的请求
        const originalOpen = XMLHttpRequest.prototype.open;
        XMLHttpRequest.prototype.open = function(method, url, ...args) {
            if (typeof url === 'string' && url.endsWith('.map')) {
                // 将URL重定向到一个空文件
                url = 'data:,'; // 空数据 URL
            }
            return originalOpen.call(this, method, url, ...args);
        };
    </script>
</head>
<body>
    <!-- 加载动画 -->
    <div class="loading-overlay d-none">
        <div class="loading-spinner"></div>
    </div>

    <!-- 侧边栏 -->
    <aside class="sidebar" id="sidebar">
        <div class="sidebar-brand">
            <h3><i class="bi bi-trophy me-2"></i>运动会管理系统</h3>
        </div>
        <div class="sidebar-divider"></div>
        {% if session.username %}
        <nav class="sidebar-nav">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link {% if request.path == url_for('index') %}active{% endif %}" href="{{ url_for('index') }}">
                        <i class="bi bi-house"></i>
                        <span>首页</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if '/department' in request.path %}active{% endif %}" href="{{ url_for('department.index') }}">
                        <i class="bi bi-building"></i>
                        <span>院系管理</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if '/athlete' in request.path %}active{% endif %}" href="{{ url_for('athlete.index') }}">
                        <i class="bi bi-person"></i>
                        <span>运动员管理</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if '/event' in request.path and '/event_athlete' not in request.path %}active{% endif %}" href="{{ url_for('event.index') }}">
                        <i class="bi bi-calendar-event"></i>
                        <span>比赛项目管理</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if '/event_athlete' in request.path %}active{% endif %}" href="{{ url_for('event_athlete.index') }}">
                        <i class="bi bi-people"></i>
                        <span>项目学生管理</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if '/judge' in request.path %}active{% endif %}" href="{{ url_for('judge.index') }}">
                        <i class="bi bi-flag"></i>
                        <span>裁判员管理</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if '/score' in request.path and 'ranking' not in request.path %}active{% endif %}" href="{{ url_for('score.index') }}">
                        <i class="bi bi-clipboard-data"></i>
                        <span>成绩管理</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if '/score/department_ranking' in request.path %}active{% endif %}" href="{{ url_for('score.department_ranking') }}">
                        <i class="bi bi-bar-chart"></i>
                        <span>院系排名</span>
                    </a>
                </li>
            </ul>
        </nav>
        {% endif %}
    </aside>

    <!-- 顶部信息栏 -->
    <header class="header-container">
        <div class="container-fluid d-flex justify-content-between align-items-center">
            <div>
                <button id="toggle-sidebar" class="btn btn-link text-dark">
                    <i class="bi bi-list fs-4"></i>
                </button>
            </div>
            {% if session.username %}
            <div class="user-info dropdown">
                <a class="dropdown-toggle text-decoration-none" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                    <i class="bi bi-person-circle"></i>
                    <span class="user-name">{{ session.username }}</span>
                </a>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                        <i class="bi bi-box-arrow-right me-2"></i>退出登录
                    </a></li>
                </ul>
            </div>
            {% endif %}
        </div>
    </header>

    <!-- 主内容区 -->
    <div class="content-wrapper">
        <!-- 消息提示 -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- 页面标题 -->
        <div class="page-title">
            <h1>{% block page_title %}运动会管理系统{% endblock %}</h1>
        </div>

        <!-- 页面内容 -->
        {% block content %}{% endblock %}

    </div>

    <!-- 页脚 -->
    <footer class="footer mt-auto py-3 bg-light text-center">
        <div class="container">
            <span class="text-muted">© {{ now.year }} 运动会管理系统 | 版权所有</span>
        </div>
    </footer>

    <!-- jQuery -->
    <script src="{{ url_for('static', filename='vendor/code.jquery.com/jquery-3.6.0.min_3a66d0e5.js') }}"></script>
    <!-- Bootstrap 5 JS -->
    <script src="{{ url_for('static', filename='vendor/cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min_6b7a626f.js') }}" data-no-sourcemap></script>
    <!-- DataTables JS -->
    <script src="{{ url_for('static', filename='js/jquery.dataTables.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/dataTables.bootstrap5.min.js') }}"></script>
    <!-- Select2 JS -->
    <script src="{{ url_for('static', filename='vendor/select2/select2.min.js') }}"></script>
    <!-- 自定义JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    <script>
        // 侧边栏切换
        document.addEventListener('DOMContentLoaded', function() {
            const toggleBtn = document.getElementById('toggle-sidebar');
            const sidebar = document.getElementById('sidebar');
            const contentWrapper = document.querySelector('.content-wrapper');

            if (toggleBtn) {
                toggleBtn.addEventListener('click', function() {
                    sidebar.classList.toggle('collapsed');
                    contentWrapper.classList.toggle('expanded');
                });
            }

            // 添加页面加载完成后的动画效果
            setTimeout(() => {
                document.body.classList.add('loaded');
            }, 200);
        });
    </script>
    {% block scripts %}{% endblock %}
</body>
</html>
