{% extends "layout.html" %}

{% block title %}添加比赛项目 - 运动会管理系统{% endblock %}

{% block page_title %}添加比赛项目{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">添加新比赛项目</h5>
    </div>
    <div class="card-body">
        <form method="post" action="{{ url_for('event.add') }}">
            {{ form.csrf_token }}

            <div class="mb-3">
                <label for="event_name" class="form-label">项目名称</label>
                {{ form.event_name(class="form-control", id="event_name") }}
                {% if form.event_name.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.event_name.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% endif %}
            </div>

            <div class="mb-3">
                <label for="event_type" class="form-label">项目类型</label>
                {{ form.event_type(class="form-select", id="event_type") }}
                {% if form.event_type.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.event_type.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% endif %}
            </div>

            <div class="mb-3">
                <label for="gender_limit" class="form-label">性别限制</label>
                {{ form.gender_limit(class="form-select", id="gender_limit") }}
                {% if form.gender_limit.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.gender_limit.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% endif %}
            </div>

            <div class="mb-3">
                <label for="max_participants" class="form-label">最大参与人数</label>
                {{ form.max_participants(class="form-control", id="max_participants") }}
                {% if form.max_participants.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.max_participants.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% endif %}
                <div class="form-text">可选，留空表示不限制人数</div>
            </div>

            <div class="mb-3">
                <label for="start_time" class="form-label">开始时间</label>
                {{ form.start_time(class="form-control", id="start_time", type="datetime-local") }}
                {% if form.start_time.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.start_time.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% endif %}
                <div class="form-text">可选，请使用日期时间选择器</div>
            </div>

            <div class="mb-3">
                <label for="location" class="form-label">比赛地点</label>
                {{ form.location(class="form-control", id="location") }}
                {% if form.location.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.location.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% endif %}
                <div class="form-text">可选</div>
            </div>

            <div class="d-flex justify-content-between">
                <a href="{{ url_for('event.index') }}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> 返回
                </a>
                {{ form.submit(class="btn btn-primary") }}
            </div>
        </form>
    </div>
</div>
{% endblock %}
