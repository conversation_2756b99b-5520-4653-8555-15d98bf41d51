{% extends "layout.html" %}

{% block title %}添加裁判 - 运动会管理系统{% endblock %}

{% block page_title %}添加裁判到比赛项目{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">添加裁判到 "{{ event.event_name }}"</h5>
    </div>
    <div class="card-body">
        <div class="alert alert-info mb-4">
            <i class="bi bi-info-circle me-2"></i> 请为比赛项目分配裁判员。您可以添加多个裁判员，并为每个裁判员指定不同的角色。
        </div>
        <form method="post" action="{{ url_for('event.add_judge', event_id=event.event_id) }}">
            {{ form.csrf_token }}

            <div class="mb-3">
                <label for="judge_id" class="form-label">裁判</label>
                {{ form.judge_id(class="form-select", id="judge_id") }}
                {% if form.judge_id.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.judge_id.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% endif %}
            </div>

            <div class="mb-3">
                <label for="role" class="form-label">角色</label>
                {{ form.role(class="form-control", id="role") }}
                {% if form.role.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.role.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% endif %}
                <div class="form-text">例如：主裁判、副裁判、计时员等</div>
            </div>

            <div class="d-flex justify-content-between">
                <div>
                    <a href="{{ url_for('event.detail', event_id=event.event_id) }}" class="btn btn-secondary me-2">
                        <i class="bi bi-arrow-left"></i> 返回项目详情
                    </a>
                    <a href="{{ url_for('event.index') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-list"></i> 返回项目列表
                    </a>
                </div>
                {{ form.submit(class="btn btn-primary") }}
            </div>
        </form>
    </div>
</div>
{% endblock %}
