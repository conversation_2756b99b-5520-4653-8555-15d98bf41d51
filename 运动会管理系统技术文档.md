![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml24284\wps9.png)

 

 

 

《数据库原理》

 

 

**实验报告**

 **2024 ~ 2025学年第二学期**  

 

 

 

 

 

**信息科学与工程学院**

**计算机科学与工程系**

**2025年** **4**月**







## 摘要

本报告详细介绍了基于Flask+MySQL架构的运动会管理系统的数据库设计与实现。该系统采用Python Flask 2.2.3作为后端框架，MySQL 8.0作为数据库管理系统，Bootstrap 5作为前端框架，实现了运动会的全流程信息化管理。

系统主要功能包括用户认证、院系管理、运动员管理、比赛项目管理、裁判员管理、成绩管理和统计分析等模块。数据库设计遵循第三范式，采用6个核心数据表，建立了完整的实体关系模型，确保了数据的一致性和完整性。

系统采用MVC架构模式，使用Flask-SQLAlchemy作为ORM框架，Flask-Login提供用户认证，Flask-WTF提供CSRF保护，实现了安全可靠的Web应用。前端采用响应式设计，支持多设备访问，提供了良好的用户体验。

**关键词**：运动会管理、数据库设计、Flask、MySQL、Web应用





## 目录

1. [系统背景和需求分析](#1-系统背景和需求分析)
2. [数据库概念结构设计（E-R模型）](#2-数据库概念结构设计e-r模型)
3. [逻辑结构设计（关系模式集+视图）](#3-逻辑结构设计关系模式集视图)
4. [物理设计（存储结构及索引）](#4-物理设计存储结构及索引)
5. [完整性和安全性设计](#5-完整性和安全性设计)
6. [系统实现（界面+程序核心代码）](#6-系统实现界面程序核心代码)
7. [结论](







## 第一章、需求分析

### 1.1 系统背景

#### 1.1.1 运动会管理现状分析

在当今高等教育蓬勃发展的时代背景下，各高等院校的体育事业也呈现出前所未有的繁荣景象。校园运动会作为高校体育文化的重要组成部分，不仅承载着增强学生体质、培养团队精神的教育使命，更是展现学校精神风貌和组织管理水平的重要窗口。然而，随着高校规模的不断扩大和参赛人数的急剧增长，传统的运动会管理模式正面临着前所未有的挑战。

传统的运动会管理主要依赖人工操作和纸质记录，这种管理方式在小规模运动会中尚能应付，但在面对现代大型校园运动会时，其弊端日益凸显。首先，参赛人员信息的收集和整理工作量巨大，容易出现信息遗漏或错误；其次，比赛项目的安排和时间调度需要考虑多种因素，人工协调难度极大；再次，成绩统计和排名计算涉及复杂的数学运算，手工处理不仅效率低下，而且极易出错；最后，各院系之间的成绩对比和奖项评定需要大量的数据整合工作，传统方式难以胜任。

#### 1.1.2 信息化发展趋势

随着信息技术的飞速发展，特别是互联网技术和数据库技术的日趋成熟，教育信息化已成为现代高等教育发展的必然趋势。在这一背景下，运动会管理的信息化改革势在必行。通过构建基于Web技术的运动会管理系统，可以有效解决传统管理模式中存在的诸多问题，实现管理流程的标准化、数据处理的自动化和信息共享的实时化。

现代信息技术为运动会管理提供了强有力的技术支撑。数据库技术能够确保大量参赛信息的安全存储和高效检索；Web技术使得系统具备良好的跨平台兼容性和用户友好性；网络技术则为实时信息共享和远程管理提供了可能。这些技术的综合运用，为构建高效、可靠的运动会管理系统奠定了坚实的技术基础。

#### 1.1.3 系统开发的必要性

基于上述分析，开发一套专门的运动会管理系统具有重要的现实意义和迫切的实际需求。从管理效率角度来看，信息化系统能够大幅提升工作效率，减少重复性劳动，使管理人员能够将更多精力投入到运动会的组织协调工作中。从数据准确性角度来看，系统化的数据处理能够有效避免人为错误，确保成绩统计和排名计算的准确性。从用户体验角度来看，现代化的Web界面能够为用户提供更加便捷的操作体验，降低系统使用的技术门槛。

此外，运动会管理系统的建设还具有重要的示范意义。它不仅能够为本校的体育管理工作提供有力支撑，还可以为其他高校的类似系统建设提供参考和借鉴，推动整个高等教育体育管理的信息化进程。

### 1.2 系统开发目标与意义

#### 1.2.1 总体目标

本运动会管理系统的开发旨在构建一个集成化、智能化、人性化的运动会管理平台，通过现代信息技术手段，全面提升运动会组织管理的科学化水平。系统将以用户需求为导向，以技术创新为驱动，以管理效率为核心，打造一个功能完善、性能稳定、操作便捷的综合性管理系统。

#### 1.2.2 具体目标

**管理流程优化目标**：通过系统化的流程设计，实现从参赛报名到成绩发布的全流程数字化管理，建立标准化的操作规范，消除管理环节中的冗余和低效，构建科学合理的管理体系。

**数据处理自动化目标**：利用数据库技术和算法设计，实现参赛信息的自动录入、成绩数据的自动计算、排名结果的自动生成，大幅减少人工干预，提高数据处理的准确性和时效性。

**信息共享实时化目标**：建立统一的信息平台，实现各管理部门之间的信息实时共享，为决策制定提供及时、准确的数据支撑，提升管理决策的科学性和有效性。

**用户体验人性化目标**：采用现代Web技术，设计直观友好的用户界面，提供便捷的操作方式，确保不同技术水平的用户都能够轻松使用系统，提升用户满意度。

**系统安全可靠性目标**：建立完善的安全防护机制，确保系统数据的安全性和完整性，提供稳定可靠的系统运行环境，保障运动会管理工作的顺利进行。

### 1.3 需求分析

#### 1.3.1 功能需求深度分析

基于对用户角色和业务流程的深入分析，本系统的功能需求可以划分为以下几个核心模块：

**用户认证与权限管理模块**

这是系统安全性的基础保障模块。该模块不仅要实现基本的用户登录验证功能，更要建立完善的权限控制机制。通过基于角色的访问控制（RBAC）模型，确保不同类型的用户只能访问其权限范围内的功能和数据。同时，系统还需要提供安全的会话管理机制，防止会话劫持和未授权访问。

**院系组织管理模块**

院系是运动会参赛的基本组织单位，也是团体成绩统计的重要依据。该模块需要提供完整的院系信息管理功能，包括院系基本信息的维护、院系代码的标准化管理、以及院系间的层级关系处理。特别是院系代码的设计，需要考虑到统计分析的便利性和数据的可扩展性。

**运动员信息管理模块**

运动员是运动会的核心参与者，其信息管理的准确性直接影响到整个运动会的组织质量。该模块需要建立完善的运动员档案管理体系，不仅要记录基本的个人信息，还要建立与院系的关联关系，为后续的成绩统计和分析提供数据基础。同时，系统还需要提供灵活的查询和筛选功能，支持按不同条件对运动员信息进行检索。

**比赛项目管理模块**

比赛项目的科学设置是运动会成功举办的关键因素。该模块需要支持多样化的项目类型管理，包括田赛、径赛、球类、游泳等不同类别的项目。每个项目都需要详细的属性定义，如性别限制、参赛人数限制、比赛时间和地点等。系统还需要提供项目间的关联管理功能，支持复杂的比赛安排和时间调度。

**裁判员管理与分配模块**

裁判员是保证比赛公平公正的重要保障。该模块需要建立完整的裁判员信息库，记录裁判员的专业级别、执裁经验、联系方式等信息。更重要的是，系统需要提供智能的裁判员分配功能，根据比赛项目的特点和裁判员的专业背景，合理安排裁判员的执裁任务，避免利益冲突和专业不匹配的情况。

**成绩管理与统计模块**

成绩管理是整个系统的核心业务模块。该模块不仅要提供准确的成绩录入功能，还要实现自动化的排名计算和积分统计。系统需要支持多种成绩类型的处理，如时间型成绩、距离型成绩、得分型成绩等。同时，还要提供完善的数据验证机制，确保录入成绩的合理性和准确性。

**综合统计分析模块**

统计分析功能是系统的高级应用模块，为运动会的总结评估提供数据支撑。该模块需要提供多维度的统计分析功能，包括院系排名统计、个人成绩分析、项目参与度统计等。通过数据可视化技术，为用户提供直观的统计图表和分析报告。

#### 1.3.2 使用场景分析

**运动会筹备阶段场景**

在运动会筹备阶段，管理员需要进行大量的基础数据准备工作。首先是院系信息的录入和维护，确保所有参赛单位的信息准确完整；然后是比赛项目的设置和安排，包括项目类型、时间地点、参赛规则等详细信息的配置；接着是裁判员信息的收集和录入，为后续的裁判分配做好准备；最后是运动员报名信息的审核和确认，确保参赛资格的合规性。

**运动会进行阶段场景**

在运动会正式进行期间，系统需要支持实时的数据更新和查询。管理员需要根据比赛进展情况，及时录入各项目的比赛成绩，系统自动计算排名和积分；普通用户可以通过系统实时查看比赛进展和成绩情况，了解自己关注的运动员或院系的表现；裁判员可以查看自己的执裁安排，确保按时到位执行裁判任务。

**运动会总结阶段场景**

运动会结束后，系统需要提供全面的统计分析功能。管理员可以生成各类统计报告，包括院系团体总分排名、个人单项成绩排名、破纪录情况统计等；各院系负责人可以查看本单位的详细成绩情况，为后续的体育工作提供参考；学校领导可以通过系统了解整个运动会的组织效果和参与情况，为今后的体育工作决策提供数据支撑。

### 1.4 非功能性需求分析

#### 1.4.1 性能需求

考虑到运动会期间的高并发访问特点，系统必须具备良好的性能表现。页面响应时间应控制在3秒以内，确保用户获得流畅的使用体验；系统应支持至少50个并发用户同时访问，满足运动会期间的访问高峰需求；数据处理能力应支持1000名运动员和100个比赛项目的规模，为大型运动会提供充足的容量保障。

#### 1.4.2 可靠性需求

系统可用性应达到99%以上，确保在运动会关键时期的稳定运行；数据备份和恢复机制应完善，防止因系统故障导致的数据丢失；错误处理机制应健全，能够优雅地处理各种异常情况，避免系统崩溃。

#### 1.4.3 兼容性需求

系统应支持主流浏览器的访问，包括Chrome、Firefox、Safari、Edge等，确保不同用户都能正常使用；界面设计应采用响应式布局，支持PC端和移动端的访问，适应不同设备的使用需求。

### 1.5 技术架构需求

#### 1.5.1 开发技术选型

基于系统的功能需求和性能要求，本系统采用Python Flask框架作为后端开发技术。Flask框架具有轻量级、灵活性强、扩展性好的特点，非常适合中小型Web应用的开发。数据库选择MySQL 8.0，其成熟稳定的特性和强大的数据处理能力能够很好地满足系统需求。前端采用Bootstrap 5框架，确保界面的美观性和响应式特性。

#### 1.5.2 安全性要求

系统必须建立完善的安全防护机制，包括CSRF攻击防护、SQL注入防护、XSS攻击防护等，确保系统和数据的安全性；用户密码应采用安全的加密算法进行存储，防止密码泄露；系统应提供完整的操作日志记录功能，便于安全审计和问题追踪。

---

## 第二章、概念模型设计

### 2.1 系统E-R图

通过对运动会管理业务的深入分析，系统识别出以下6个核心实体及其关系：

![运动会管理系统E-R图](运动会管理系统E-R图)

**实体说明：**

1. **USERS（用户实体）**：系统用户信息，包含管理员和普通用户
2. **DEPARTMENT（院系实体）**：学校院系信息，运动员的归属单位
3. **ATHLETE（运动员实体）**：参赛运动员的基本信息
4. **EVENT（比赛项目实体）**：运动会的各项比赛项目
5. **JUDGE（裁判员实体）**：负责比赛执裁的裁判员信息
6. **SCORE（成绩实体）**：运动员在各项目中的比赛成绩
7. **EVENT_JUDGE（项目裁判关联实体）**：比赛项目与裁判员的多对多关系

**关系说明：**

- **DEPARTMENT与ATHLETE**：一对多关系，一个院系可以有多名运动员
- **ATHLETE与SCORE**：一对多关系，一名运动员可以参加多个项目
- **EVENT与SCORE**：一对多关系，一个项目可以有多个成绩记录
- **EVENT与JUDGE**：多对多关系，通过EVENT_JUDGE表实现，一个项目可以有多名裁判员，一名裁判员可以执裁多个项目

---

## 第三章、逻辑结构设计

### 3.1 关系模式转换

基于E-R图，系统设计了7个关系模式，严格遵循第三范式要求：

#### 3.1.1 E-R图到关系模式的转换过程

1. **实体转换**：每个实体转换为一个关系模式
   - USERS → users表
   - DEPARTMENT → department表
   - ATHLETE → athlete表
   - EVENT → event表
   - JUDGE → judge表
   - SCORE → score表

2. **多对多关系转换**：EVENT与JUDGE的多对多关系转换为独立的关系模式
   - EVENT_JUDGE → event_judge表

3. **一对多关系处理**：通过外键实现
   - athlete表中的dept_id外键关联department表
   - score表中的athlete_id外键关联athlete表
   - score表中的event_id外键关联event表

#### 3.1.2 关系模式列表

1. **users(user_id, username, password, real_name, email, phone, role, status, last_login, created_at, updated_at)**
2. **department(dept_id, dept_name, dept_code)**
3. **athlete(athlete_id, name, gender, student_id, dept_id)**
4. **event(event_id, event_name, event_type, gender_limit, max_participants, start_time, location)**
5. **judge(judge_id, name, level, contact)**
6. **score(score_id, athlete_id, event_id, result, ranking, points, record_time)**
7. **event_judge(event_id, judge_id, role)**

### 3.2 关系模式优化

#### 3.2.1 规范化过程

系统严格遵循数据库规范化理论：

**第一范式（1NF）**：
- 所有表的每个字段都是原子性的，不可再分
- 消除了重复组和多值属性

**第二范式（2NF）**：
- 所有非主属性完全函数依赖于主键
- 消除了部分函数依赖

**第三范式（3NF）**：
- 消除了传递函数依赖
- 每个非主属性都直接依赖于主键

#### 3.2.2 反规范化设计

为了提高查询性能，系统在某些场景下进行了适度的反规范化：

1. **冗余存储考虑**：在score表中直接存储points字段，避免复杂的积分计算
2. **查询优化**：通过视图预计算常用的统计数据

### 3.3 数据字典

#### 3.3.1 users表（用户表）

| 字段名 | 数据类型 | 长度 | 约束条件 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| user_id | INT | - | 主键自增 | - | 用户主键 |
| username | VARCHAR | 50 | UNIQUE, NOT NULL | - | 用户名 |
| password | VARCHAR | 255 | NOT NULL | - | 密码 |
| real_name | VARCHAR | 50 | - | NULL | 真实姓名 |
| email | VARCHAR | 100 | - | NULL | 邮箱地址 |
| phone | VARCHAR | 20 | - | NULL | 联系电话 |
| role | ENUM | - | NOT NULL | 'user' | 用户角色 |
| status | ENUM | - | NOT NULL | 'active' | 用户状态 |
| last_login | DATETIME | - | - | NULL | 最后登录时间 |
| created_at | TIMESTAMP | - | 默认时间 | - | 创建时间 |
| updated_at | TIMESTAMP | - | - | - | 更新时间 |

#### 3.3.2 department表（院系表）

| 字段名 | 数据类型 | 长度 | 约束条件 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| dept_id | INT | - | 主键自增 | - | 院系主键 |
| dept_name | VARCHAR | 50 | NOT NULL | - | 院系名称 |
| dept_code | VARCHAR | 10 | UNIQUE, NOT NULL | - | 院系代码 |

#### 3.3.3 athlete表（运动员表）

| 字段名 | 数据类型 | 长度 | 约束条件 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| athlete_id | INT | - | 主键自增 | - | 运动员主键 |
| name | VARCHAR | 50 | NOT NULL | - | 运动员姓名 |
| gender | ENUM | - | NOT NULL | - | 性别（男/女） |
| student_id | VARCHAR | 20 | UNIQUE, NOT NULL | - | 学号 |
| dept_id | INT | - | NOT NULL, 外键 | - | 所属院系ID |

#### 3.3.4 event表（比赛项目表）

| 字段名 | 数据类型 | 长度 | 约束条件 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| event_id | INT | - | 主键自增 | - | 项目主键 |
| event_name | VARCHAR | 100 | NOT NULL | - | 项目名称 |
| event_type | ENUM | - | NOT NULL | - | 项目类型 |
| gender_limit | ENUM | - | - | '不限' | 性别限制 |
| max_participants | INT | - | - | NULL | 最大参与人数 |
| start_time | DATETIME | - | - | NULL | 开始时间 |
| location | VARCHAR | 50 | - | NULL | 比赛地点 |

#### 3.3.5 judge表（裁判员表）

| 字段名 | 数据类型 | 长度 | 约束条件 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| judge_id | INT | - | 主键自增 | - | 裁判员主键 |
| name | VARCHAR | 50 | NOT NULL | - | 裁判员姓名 |
| level | VARCHAR | 20 | - | NULL | 裁判员级别 |
| contact | VARCHAR | 20 | - | NULL | 联系方式 |

#### 3.3.6 score表（成绩表）

| 字段名 | 数据类型 | 长度 | 约束条件 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| score_id | INT | - | 主键自增 | - | 成绩主键 |
| athlete_id | INT | - | NOT NULL, 外键 | - | 运动员ID |
| event_id | INT | - | NOT NULL, 外键 | - | 项目ID |
| result | VARCHAR | 20 | - | NULL | 成绩结果 |
| ranking | INT | - | - | NULL | 排名 |
| points | INT | - | - | NULL | 得分 |
| record_time | TIMESTAMP | - | 默认时间 | - | 记录时间 |

#### 3.3.7 event_judge表（项目裁判关联表）

| 字段名 | 数据类型 | 长度 | 约束条件 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| event_id | INT | - | PRIMARY KEY, 外键 | - | 项目ID |
| judge_id | INT | - | PRIMARY KEY, 外键 | - | 裁判员ID |
| role | VARCHAR | 20 | - | NULL | 裁判角色 |

---

## 第四章、物理结构设计与实施

### 4.1 数据库和表的创建

#### 4.1.1 数据库创建脚本

```sql
-- 创建数据库
CREATE DATABASE IF NOT EXISTS sports_meetDB;
USE sports_meetDB;

-- 设置字符集
SET NAMES utf8mb4;
SET CHARACTER SET utf8mb4;
```

#### 4.1.2 表结构创建脚本

**用户表创建：**
```sql
CREATE TABLE users (
    user_id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    real_name VARCHAR(50),
    email VARCHAR(100),
    phone VARCHAR(20),
    role ENUM('admin', 'manager', 'user') NOT NULL DEFAULT 'user',
    status ENUM('active', 'inactive', 'locked') NOT NULL DEFAULT 'active',
    last_login DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

**院系表创建：**
```sql
CREATE TABLE department (
    dept_id INT PRIMARY KEY AUTO_INCREMENT,
    dept_name VARCHAR(50) NOT NULL,
    dept_code VARCHAR(10) UNIQUE NOT NULL
);
```

**运动员表创建：**
```sql
CREATE TABLE athlete (
    athlete_id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL,
    gender ENUM('男','女') NOT NULL,
    student_id VARCHAR(20) UNIQUE NOT NULL,
    dept_id INT NOT NULL,
    FOREIGN KEY (dept_id) REFERENCES department(dept_id)
);
```

**比赛项目表创建：**
```sql
CREATE TABLE event (
    event_id INT PRIMARY KEY AUTO_INCREMENT,
    event_name VARCHAR(100) NOT NULL,
    event_type ENUM('田赛','径赛','球类','游泳') NOT NULL,
    gender_limit ENUM('男','女','不限') DEFAULT '不限',
    max_participants INT,
    start_time DATETIME,
    location VARCHAR(50)
);
```

**裁判员表创建：**
```sql
CREATE TABLE judge (
    judge_id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL,
    level VARCHAR(20),
    contact VARCHAR(20)
);
```

**成绩表创建：**
```sql
CREATE TABLE score (
    score_id INT PRIMARY KEY AUTO_INCREMENT,
    athlete_id INT NOT NULL,
    event_id INT NOT NULL,
    result VARCHAR(20),
    ranking INT,
    points INT,
    record_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (athlete_id) REFERENCES athlete(athlete_id),
    FOREIGN KEY (event_id) REFERENCES event(event_id),
    UNIQUE KEY (athlete_id, event_id)
);
```

**项目裁判关联表创建：**
```sql
CREATE TABLE event_judge (
    event_id INT,
    judge_id INT,
    role VARCHAR(20),
    PRIMARY KEY (event_id, judge_id),
    FOREIGN KEY (event_id) REFERENCES event(event_id),
    FOREIGN KEY (judge_id) REFERENCES judge(judge_id)
);
```

### 4.2 查询设计

#### 4.2.1 单表查询

**1. 查询所有运动员信息**
```sql
-- 用途：运动员列表页面数据加载
SELECT athlete_id, name, gender, student_id, dept_id
FROM athlete
ORDER BY athlete_id;
```

**2. 查询特定院系的运动员**
```sql
-- 用途：按院系筛选运动员
SELECT athlete_id, name, gender, student_id
FROM athlete
WHERE dept_id = ?
ORDER BY name;
```

**3. 查询所有比赛项目**
```sql
-- 用途：比赛项目列表页面
SELECT event_id, event_name, event_type, gender_limit, start_time, location
FROM event
ORDER BY start_time;
```

**4. 查询特定类型的比赛项目**
```sql
-- 用途：按项目类型筛选
SELECT event_id, event_name, start_time, location
FROM event
WHERE event_type = ?
ORDER BY start_time;
```

**5. 查询所有院系信息**
```sql
-- 用途：院系管理页面
SELECT dept_id, dept_name, dept_code
FROM department
ORDER BY dept_code;
```

**6. 查询所有裁判员信息**
```sql
-- 用途：裁判员管理页面
SELECT judge_id, name, level, contact
FROM judge
ORDER BY level, name;
```

#### 4.2.2 多表查询

**1. 查询运动员详细信息（包含院系）**
```sql
-- 用途：运动员详情页面，API数据接口
SELECT
    a.athlete_id,
    a.name,
    a.gender,
    a.student_id,
    d.dept_name,
    d.dept_code
FROM athlete a
LEFT JOIN department d ON a.dept_id = d.dept_id
ORDER BY a.athlete_id;
```

**2. 查询运动员成绩信息**
```sql
-- 用途：成绩管理页面，运动员成绩查询
SELECT
    s.score_id,
    a.name AS athlete_name,
    a.student_id,
    e.event_name,
    s.result,
    s.ranking,
    s.points,
    s.record_time
FROM score s
JOIN athlete a ON s.athlete_id = a.athlete_id
JOIN event e ON s.event_id = e.event_id
ORDER BY s.record_time DESC;
```

**3. 查询院系排名统计**
```sql
-- 用途：院系排名页面，统计分析
SELECT
    d.dept_name,
    d.dept_code,
    COUNT(DISTINCT a.athlete_id) as athlete_count,
    COUNT(s.score_id) as score_count,
    SUM(s.points) as total_points
FROM department d
LEFT JOIN athlete a ON d.dept_id = a.dept_id
LEFT JOIN score s ON a.athlete_id = s.athlete_id
GROUP BY d.dept_id, d.dept_name, d.dept_code
ORDER BY total_points DESC;
```

**4. 查询比赛项目参与情况**
```sql
-- 用途：比赛项目统计分析
SELECT
    e.event_name,
    e.event_type,
    e.start_time,
    COUNT(s.score_id) as participant_count,
    COUNT(ej.judge_id) as judge_count
FROM event e
LEFT JOIN score s ON e.event_id = s.event_id
LEFT JOIN event_judge ej ON e.event_id = ej.event_id
GROUP BY e.event_id, e.event_name, e.event_type, e.start_time
ORDER BY e.start_time;
```

**5. 查询项目裁判分配情况**
```sql
-- 用途：裁判员分配管理
SELECT
    e.event_name,
    j.name AS judge_name,
    j.level,
    ej.role
FROM event_judge ej
JOIN event e ON ej.event_id = e.event_id
JOIN judge j ON ej.judge_id = j.judge_id
ORDER BY e.event_name, ej.role;
```

#### 4.2.3 子查询

**1. 查询每个项目的最高分**
```sql
-- 用途：项目冠军查询
SELECT
    e.event_name,
    a.name AS champion_name,
    s.result,
    s.points
FROM score s
JOIN athlete a ON s.athlete_id = a.athlete_id
JOIN event e ON s.event_id = e.event_id
WHERE s.points = (
    SELECT MAX(points)
    FROM score s2
    WHERE s2.event_id = s.event_id
)
ORDER BY e.event_name;
```

**2. 查询参赛项目数量最多的运动员**
```sql
-- 用途：活跃运动员统计
SELECT
    a.name,
    a.student_id,
    d.dept_name,
    COUNT(s.score_id) as event_count
FROM athlete a
JOIN department d ON a.dept_id = d.dept_id
JOIN score s ON a.athlete_id = s.athlete_id
GROUP BY a.athlete_id, a.name, a.student_id, d.dept_name
HAVING COUNT(s.score_id) = (
    SELECT MAX(event_count)
    FROM (
        SELECT COUNT(score_id) as event_count
        FROM score
        GROUP BY athlete_id
    ) as max_events
);
```

**3. 查询未参加任何比赛的运动员**
```sql
-- 用途：运动员参赛情况检查
SELECT
    a.athlete_id,
    a.name,
    a.student_id,
    d.dept_name
FROM athlete a
JOIN department d ON a.dept_id = d.dept_id
WHERE a.athlete_id NOT IN (
    SELECT DISTINCT athlete_id
    FROM score
    WHERE athlete_id IS NOT NULL
)
ORDER BY d.dept_name, a.name;
```

### 4.3 索引设计

#### 4.3.1 主键索引

系统为所有表自动创建主键索引：

```sql
-- 主键索引（自动创建）
-- users表主键索引
ALTER TABLE users ADD PRIMARY KEY (user_id);

-- department表主键索引
ALTER TABLE department ADD PRIMARY KEY (dept_id);

-- athlete表主键索引
ALTER TABLE athlete ADD PRIMARY KEY (athlete_id);

-- event表主键索引
ALTER TABLE event ADD PRIMARY KEY (event_id);

-- judge表主键索引
ALTER TABLE judge ADD PRIMARY KEY (judge_id);

-- score表主键索引
ALTER TABLE score ADD PRIMARY KEY (score_id);

-- event_judge表复合主键索引
ALTER TABLE event_judge ADD PRIMARY KEY (event_id, judge_id);
```

#### 4.3.2 唯一索引

为保证数据唯一性创建的索引：

```sql
-- 用户名唯一索引
CREATE UNIQUE INDEX idx_users_username ON users(username);

-- 院系代码唯一索引
CREATE UNIQUE INDEX idx_department_code ON department(dept_code);

-- 学号唯一索引
CREATE UNIQUE INDEX idx_athlete_student_id ON athlete(student_id);

-- 运动员项目唯一索引（防止重复参赛）
CREATE UNIQUE INDEX idx_score_athlete_event ON score(athlete_id, event_id);
```

#### 4.3.3 外键索引

为提高关联查询性能创建的索引：

```sql
-- 运动员院系外键索引
CREATE INDEX idx_athlete_dept_id ON athlete(dept_id);

-- 成绩运动员外键索引
CREATE INDEX idx_score_athlete_id ON score(athlete_id);

-- 成绩项目外键索引
CREATE INDEX idx_score_event_id ON score(event_id);

-- 项目裁判关联索引
CREATE INDEX idx_event_judge_event_id ON event_judge(event_id);
CREATE INDEX idx_event_judge_judge_id ON event_judge(judge_id);
```

#### 4.3.4 复合索引

根据查询需求创建的复合索引：

```sql
-- 成绩排名复合索引（用于排名查询）
CREATE INDEX idx_score_event_ranking ON score(event_id, ranking);

-- 用户角色状态复合索引（用于权限验证）
CREATE INDEX idx_users_role_status ON users(role, status);

-- 比赛项目类型时间索引（用于项目筛选）
CREATE INDEX idx_event_type_time ON event(event_type, start_time);
```

### 4.4 视图的创建

#### 4.4.1 运动员详细信息视图

```sql
-- 创建运动员详细信息视图
CREATE VIEW athlete_detail_view AS
SELECT
    a.athlete_id,
    a.name,
    a.gender,
    a.student_id,
    d.dept_name,
    d.dept_code,
    COUNT(s.score_id) as event_count,
    SUM(s.points) as total_points,
    AVG(s.ranking) as avg_ranking
FROM athlete a
LEFT JOIN department d ON a.dept_id = d.dept_id
LEFT JOIN score s ON a.athlete_id = s.athlete_id
GROUP BY a.athlete_id, a.name, a.gender, a.student_id, d.dept_name, d.dept_code;
```

**用途说明：**
- 简化运动员信息查询
- 提供运动员参赛统计数据
- 用于运动员列表页面和详情页面

#### 4.4.2 院系排名统计视图

```sql
-- 创建院系排名统计视图
CREATE VIEW department_ranking_view AS
SELECT
    d.dept_id,
    d.dept_name,
    d.dept_code,
    COUNT(DISTINCT a.athlete_id) as athlete_count,
    COUNT(s.score_id) as score_count,
    SUM(s.points) as total_points,
    RANK() OVER (ORDER BY SUM(s.points) DESC) as ranking
FROM department d
LEFT JOIN athlete a ON d.dept_id = a.dept_id
LEFT JOIN score s ON a.athlete_id = s.athlete_id
GROUP BY d.dept_id, d.dept_name, d.dept_code
ORDER BY total_points DESC;
```

**用途说明：**
- 提供院系排名数据
- 用于院系排名页面显示
- 支持实时排名计算

#### 4.4.3 比赛项目统计视图

```sql
-- 创建比赛项目统计视图
CREATE VIEW event_statistics_view AS
SELECT
    e.event_id,
    e.event_name,
    e.event_type,
    e.gender_limit,
    e.start_time,
    e.location,
    COUNT(s.score_id) as participant_count,
    COUNT(ej.judge_id) as judge_count,
    MAX(s.points) as max_points,
    MIN(s.points) as min_points,
    AVG(s.points) as avg_points
FROM event e
LEFT JOIN score s ON e.event_id = s.event_id
LEFT JOIN event_judge ej ON e.event_id = ej.event_id
GROUP BY e.event_id, e.event_name, e.event_type, e.gender_limit, e.start_time, e.location;
```

**用途说明：**
- 提供比赛项目统计信息
- 用于项目管理页面
- 支持项目参与度分析

### 4.5 存储过程、函数或触发器的创建

#### 4.5.1 存储过程

**1. 计算院系总分存储过程**

```sql
DELIMITER //
CREATE PROCEDURE CalculateDepartmentTotalPoints(IN dept_id_param INT, OUT total_points INT)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE points_sum INT DEFAULT 0;

    SELECT SUM(s.points) INTO points_sum
    FROM score s
    JOIN athlete a ON s.athlete_id = a.athlete_id
    WHERE a.dept_id = dept_id_param;

    SET total_points = IFNULL(points_sum, 0);
END //
DELIMITER ;
```

**功能说明：**
- 计算指定院系的总积分
- 输入参数：院系ID
- 输出参数：总积分
- 用途：院系排名计算

**2. 批量更新成绩排名存储过程**

```sql
DELIMITER //
CREATE PROCEDURE UpdateEventRankings(IN event_id_param INT)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE athlete_id_var INT;
    DECLARE rank_num INT DEFAULT 1;

    DECLARE ranking_cursor CURSOR FOR
        SELECT athlete_id
        FROM score
        WHERE event_id = event_id_param
        ORDER BY points DESC, result ASC;

    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

    OPEN ranking_cursor;

    ranking_loop: LOOP
        FETCH ranking_cursor INTO athlete_id_var;
        IF done THEN
            LEAVE ranking_loop;
        END IF;

        UPDATE score
        SET ranking = rank_num
        WHERE athlete_id = athlete_id_var AND event_id = event_id_param;

        SET rank_num = rank_num + 1;
    END LOOP;

    CLOSE ranking_cursor;
END //
DELIMITER ;
```

**功能说明：**
- 批量更新指定项目的成绩排名
- 按积分降序、成绩升序排列
- 用途：成绩录入后自动更新排名

#### 4.5.2 存储函数

**1. 获取运动员参赛项目数量函数**

```sql
DELIMITER //
CREATE FUNCTION GetAthleteEventCount(athlete_id_param INT)
RETURNS INT
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE event_count INT DEFAULT 0;

    SELECT COUNT(*) INTO event_count
    FROM score
    WHERE athlete_id = athlete_id_param;

    RETURN event_count;
END //
DELIMITER ;
```

**功能说明：**
- 返回指定运动员的参赛项目数量
- 输入参数：运动员ID
- 返回值：参赛项目数量
- 用途：运动员统计分析

**2. 计算院系平均分函数**

```sql
DELIMITER //
CREATE FUNCTION GetDepartmentAveragePoints(dept_id_param INT)
RETURNS DECIMAL(10,2)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE avg_points DECIMAL(10,2) DEFAULT 0.00;

    SELECT AVG(s.points) INTO avg_points
    FROM score s
    JOIN athlete a ON s.athlete_id = a.athlete_id
    WHERE a.dept_id = dept_id_param;

    RETURN IFNULL(avg_points, 0.00);
END //
DELIMITER ;
```

**功能说明：**
- 计算指定院系的平均积分
- 输入参数：院系ID
- 返回值：平均积分（保留2位小数）
- 用途：院系成绩分析

#### 4.5.3 触发器

**1. 成绩插入后自动更新排名触发器**

```sql
DELIMITER //
CREATE TRIGGER tr_score_after_insert
AFTER INSERT ON score
FOR EACH ROW
BEGIN
    -- 更新该项目所有成绩的排名
    CALL UpdateEventRankings(NEW.event_id);
END //
DELIMITER ;
```

**功能说明：**
- 在插入新成绩后自动触发
- 调用存储过程更新项目排名
- 确保排名数据的实时性

**2. 成绩更新后自动更新排名触发器**

```sql
DELIMITER //
CREATE TRIGGER tr_score_after_update
AFTER UPDATE ON score
FOR EACH ROW
BEGIN
    -- 如果积分发生变化，更新排名
    IF NEW.points != OLD.points THEN
        CALL UpdateEventRankings(NEW.event_id);
    END IF;
END //
DELIMITER ;
```

**功能说明：**
- 在更新成绩后自动触发
- 仅在积分变化时更新排名
- 提高系统性能

**3. 运动员删除前检查触发器**

```sql
DELIMITER //
CREATE TRIGGER tr_athlete_before_delete
BEFORE DELETE ON athlete
FOR EACH ROW
BEGIN
    DECLARE score_count INT DEFAULT 0;

    -- 检查是否有成绩记录
    SELECT COUNT(*) INTO score_count
    FROM score
    WHERE athlete_id = OLD.athlete_id;

    -- 如果有成绩记录，阻止删除
    IF score_count > 0 THEN
        SIGNAL SQLSTATE '45000'
        SET MESSAGE_TEXT = '该运动员已有成绩记录，无法删除';
    END IF;
END //
DELIMITER ;
```

**功能说明：**
- 在删除运动员前检查是否有成绩记录
- 防止误删除有成绩的运动员
- 维护数据完整性

## 5. 完整性和安全性设计

### 5.1 数据完整性约束

#### 5.1.1 实体完整性

通过主键约束确保实体完整性：

- 所有表都定义了主键
- 主键字段不允许为空
- 主键值唯一且自增

#### 5.1.2 参照完整性

通过外键约束确保参照完整性：

```sql
-- 运动员表外键约束
ALTER TABLE athlete
ADD CONSTRAINT fk_athlete_department
FOREIGN KEY (dept_id) REFERENCES department(dept_id);

-- 成绩表外键约束
ALTER TABLE score
ADD CONSTRAINT fk_score_athlete
FOREIGN KEY (athlete_id) REFERENCES athlete(athlete_id);

ALTER TABLE score
ADD CONSTRAINT fk_score_event
FOREIGN KEY (event_id) REFERENCES event(event_id);

-- 项目裁判关联表外键约束
ALTER TABLE event_judge
ADD CONSTRAINT fk_event_judge_event
FOREIGN KEY (event_id) REFERENCES event(event_id);

ALTER TABLE event_judge
ADD CONSTRAINT fk_event_judge_judge
FOREIGN KEY (judge_id) REFERENCES judge(judge_id);
```

#### 5.1.3 域完整性

通过数据类型和约束确保域完整性：

1. **枚举约束**：
   - gender: ENUM('男','女')
   - event_type: ENUM('田赛','径赛','球类','游泳')
   - role: ENUM('admin', 'manager', 'user')

2. **非空约束**：关键字段设置NOT NULL
3. **长度约束**：字符串字段设置合适的长度限制
4. **唯一约束**：用户名、学号、院系代码等设置唯一约束

#### 5.1.4 用户定义完整性

通过触发器和检查约束实现业务规则：

```sql
-- 成绩排名检查约束
ALTER TABLE score
ADD CONSTRAINT chk_ranking
CHECK (ranking > 0);

-- 得分检查约束
ALTER TABLE score
ADD CONSTRAINT chk_points
CHECK (points >= 0);

-- 最大参与人数检查约束
ALTER TABLE event
ADD CONSTRAINT chk_max_participants
CHECK (max_participants > 0);
```

### 5.2 安全性设计

#### 5.2.1 用户认证机制

系统采用Flask-Login进行用户认证：

```python
# 用户模型实现Flask-Login接口
class User(db.Model, UserMixin):
    __tablename__ = 'users'

    username = db.Column(db.String(20), primary_key=True)
    password = db.Column(db.String(100), nullable=False)
    role = db.Column(db.String(20), default='user')

    def get_id(self):
        return self.username

    @property
    def is_authenticated(self):
        return True

    @property
    def is_active(self):
        return True

    @property
    def is_anonymous(self):
        return False
```

**认证流程：**

1. 用户提交登录表单
2. 系统验证用户名和密码
3. 验证成功后创建用户会话
4. 后续请求通过会话验证用户身份

#### 5.2.2 权限控制机制

系统实现了基于角色的访问控制（RBAC）：

```python
# 管理员权限装饰器
def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or session.get('role') != 'admin':
            flash('您没有权限访问该页面！', 'danger')
            return redirect(url_for('auth.login'))
        return f(*args, **kwargs)
    return decorated_function

# 使用示例
@athlete_bp.route('/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add():
    # 只有管理员可以添加运动员
    pass
```

#### 5.2.3 CSRF保护

使用Flask-WTF提供CSRF保护：

```python
# 应用初始化时启用CSRF保护
from flask_wtf.csrf import CSRFProtect

app = Flask(__name__)
csrf = CSRFProtect(app)

# 表单类自动包含CSRF令牌
class AthleteForm(FlaskForm):
    name = StringField('姓名', validators=[DataRequired()])
    # CSRF令牌自动添加
    submit = SubmitField('提交')
```

#### 5.2.4 SQL注入防护

使用SQLAlchemy ORM防止SQL注入：

```python
# 安全的查询方式
athlete = Athlete.query.filter_by(student_id=student_id).first()

# 参数化查询
athletes = Athlete.query.filter(
    Athlete.name.like(f'%{search_term}%')
).all()
```

#### 5.2.5 XSS防护

模板自动转义和输入验证：

```html
<!-- Jinja2模板自动转义 -->
<td>{{ athlete.name }}</td>

<!-- 手动转义 -->
<td>{{ athlete.description|e }}</td>
```

#### 5.2.6 会话安全

```python
# 会话配置
app.config['SESSION_COOKIE_SECURE'] = True  # HTTPS only
app.config['SESSION_COOKIE_HTTPONLY'] = True  # 防止XSS
app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'  # CSRF保护
```



## 第六章、系统的实现

### 6.1 用户认证管理

**功能描述：**
- 用户登录验证
- 权限控制和会话管理
- 用户状态管理

**核心实现：**

```python
# modules/auth.py - 用户认证模块
@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('index'))

    form = LoginForm()

    if form.validate_on_submit():
        username = form.username.data
        password = form.password.data

        user = User.query.filter_by(username=username).first()

        if user and user.password == password:
            login_user(user)
            session['username'] = user.username
            session['role'] = user.role

            next_page = request.args.get('next')
            flash('登录成功！', 'success')
            return redirect(next_page or url_for('index'))
        else:
            flash('用户名或密码错误！', 'danger')

    return render_template('login.html', form=form)

# 权限控制装饰器
def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or session.get('role') != 'admin':
            flash('您没有权限访问该页面！', 'danger')
            return redirect(url_for('auth.login'))
        return f(*args, **kwargs)
    return decorated_function
```



### 6.2 院系管理模块

**功能描述：**
- 院系信息的增删改查
- 院系代码管理
- 院系统计分析

**核心实现：**

```python
# modules/department.py - 院系管理模块
@department_bp.route('/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add():
    form = DepartmentForm()

    if form.validate_on_submit():
        # 检查院系代码是否已存在
        existing_dept = Department.query.filter_by(dept_code=form.dept_code.data).first()
        if existing_dept:
            flash('院系代码已存在！', 'danger')
            return render_template('department/add.html', form=form)

        # 创建新院系
        department = Department(
            dept_name=form.dept_name.data,
            dept_code=form.dept_code.data
        )

        db.session.add(department)
        db.session.commit()

        flash('院系添加成功！', 'success')
        return redirect(url_for('department.index'))

    return render_template('department/add.html', form=form)

@department_bp.route('/api/list')
@login_required
def api_list():
    departments = Department.query.all()
    result = []

    for dept in departments:
        # 统计院系运动员数量
        athlete_count = Athlete.query.filter_by(dept_id=dept.dept_id).count()

        # 统计院系总积分
        total_points = db.session.query(func.sum(Score.points)).join(Athlete).filter(
            Athlete.dept_id == dept.dept_id
        ).scalar() or 0

        result.append({
            'dept_id': dept.dept_id,
            'dept_name': dept.dept_name,
            'dept_code': dept.dept_code,
            'athlete_count': athlete_count,
            'total_points': float(total_points)
        })

    return jsonify(result)
```

### 6.3 运动员管理模块

**功能描述：**
- 运动员注册和信息管理
- 按院系分类管理
- 运动员详细信息查看
- 运动员成绩查询

**核心实现：**

```python
# modules/athlete.py - 运动员管理模块
@athlete_bp.route('/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add():
    form = AthleteForm()

    if form.validate_on_submit():
        # 检查学号是否已存在
        existing_athlete = Athlete.query.filter_by(student_id=form.student_id.data).first()
        if existing_athlete:
            flash('学号已存在！', 'danger')
            return render_template('athlete/add.html', form=form)

        # 创建新运动员
        athlete = Athlete(
            name=form.name.data,
            gender=form.gender.data,
            student_id=form.student_id.data,
            dept_id=form.dept_id.data
        )

        db.session.add(athlete)
        db.session.commit()

        flash('运动员添加成功！', 'success')
        return redirect(url_for('athlete.index'))

    return render_template('athlete/add.html', form=form)

@athlete_bp.route('/detail/<int:athlete_id>')
@login_required
def detail(athlete_id):
    athlete = Athlete.query.get_or_404(athlete_id)

    # 获取运动员成绩
    scores = db.session.query(Score, Event).join(Event).filter(
        Score.athlete_id == athlete_id
    ).order_by(Score.record_time.desc()).all()

    # 计算统计数据
    total_points = sum(score.points for score, event in scores if score.points)
    event_count = len(scores)
    avg_ranking = sum(score.ranking for score, event in scores if score.ranking) / event_count if event_count > 0 else 0

    return render_template('athlete/detail.html',
                         athlete=athlete,
                         scores=scores,
                         total_points=total_points,
                         event_count=event_count,
                         avg_ranking=round(avg_ranking, 2))
```

### 6.4 比赛项目管理模块

**功能描述：**
- 比赛项目设置和管理
- 项目分类管理（田赛、径赛、球类、游泳）
- 比赛时间和地点安排
- 性别限制设置

**核心实现：**

```python
# modules/event.py - 比赛项目管理模块
@event_bp.route('/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add():
    form = EventForm()

    if form.validate_on_submit():
        event = Event(
            event_name=form.event_name.data,
            event_type=form.event_type.data,
            gender_limit=form.gender_limit.data,
            max_participants=form.max_participants.data,
            start_time=form.start_time.data,
            location=form.location.data
        )

        db.session.add(event)
        db.session.commit()

        flash('比赛项目添加成功！', 'success')
        return redirect(url_for('event.index'))

    return render_template('event/add.html', form=form)

@event_bp.route('/api/list')
@login_required
def api_list():
    events = Event.query.all()
    result = []

    for event in events:
        # 统计参赛人数
        participant_count = Score.query.filter_by(event_id=event.event_id).count()

        # 统计裁判员数量
        judge_count = EventJudge.query.filter_by(event_id=event.event_id).count()

        result.append({
            'event_id': event.event_id,
            'event_name': event.event_name,
            'event_type': event.event_type,
            'gender_limit': event.gender_limit,
            'start_time': event.start_time.strftime('%Y-%m-%d %H:%M') if event.start_time else '',
            'location': event.location,
            'participant_count': participant_count,
            'judge_count': judge_count
        })

    return jsonify(result)
```

### 6.5 裁判员管理模块

**功能描述：**
- 裁判员信息管理
- 裁判员级别分类（国家级、一级、二级、三级）
- 项目裁判分配管理

**核心实现：**

```python
# modules/judge.py - 裁判员管理模块
@judge_bp.route('/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add():
    form = JudgeForm()

    if form.validate_on_submit():
        judge = Judge(
            name=form.name.data,
            level=form.level.data,
            contact=form.contact.data
        )

        db.session.add(judge)
        db.session.commit()

        flash('裁判员添加成功！', 'success')
        return redirect(url_for('judge.index'))

    return render_template('judge/add.html', form=form)

@judge_bp.route('/assign/<int:event_id>', methods=['GET', 'POST'])
@login_required
@admin_required
def assign(event_id):
    event = Event.query.get_or_404(event_id)
    form = JudgeAssignForm()

    if form.validate_on_submit():
        # 检查是否已分配
        existing_assignment = EventJudge.query.filter_by(
            event_id=event_id,
            judge_id=form.judge_id.data
        ).first()

        if existing_assignment:
            flash('该裁判员已分配到此项目！', 'warning')
        else:
            assignment = EventJudge(
                event_id=event_id,
                judge_id=form.judge_id.data,
                role=form.role.data
            )

            db.session.add(assignment)
            db.session.commit()

            flash('裁判员分配成功！', 'success')

        return redirect(url_for('judge.assign', event_id=event_id))

    # 获取已分配的裁判员
    assigned_judges = db.session.query(Judge, EventJudge).join(EventJudge).filter(
        EventJudge.event_id == event_id
    ).all()

    return render_template('judge/assign.html',
                         event=event,
                         form=form,
                         assigned_judges=assigned_judges)
```

### 6.6 成绩管理模块

**功能描述：**
- 成绩录入和修改
- 成绩排名计算
- 积分统计
- 性别限制验证

**核心实现：**

```python
# modules/score.py - 成绩管理模块
@score_bp.route('/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add():
    form = ScoreForm()

    if form.validate_on_submit():
        athlete_id = form.athlete_id.data
        event_id = form.event_id.data

        # 检查是否已有成绩记录
        existing_score = Score.query.filter_by(
            athlete_id=athlete_id,
            event_id=event_id
        ).first()

        if existing_score:
            flash('该运动员在此项目已有成绩记录！', 'warning')
            return render_template('score/add.html', form=form)

        # 验证性别限制
        athlete = Athlete.query.get(athlete_id)
        event = Event.query.get(event_id)

        if event.gender_limit != '不限' and athlete.gender != event.gender_limit:
            flash(f'该项目限制{event.gender_limit}性参加！', 'danger')
            return render_template('score/add.html', form=form)

        # 创建成绩记录
        score = Score(
            athlete_id=athlete_id,
            event_id=event_id,
            result=form.result.data,
            ranking=form.ranking.data,
            points=form.points.data
        )

        db.session.add(score)
        db.session.commit()

        flash('成绩添加成功！', 'success')
        return redirect(url_for('score.index'))

    return render_template('score/add.html', form=form)

@score_bp.route('/api/ranking/<int:event_id>')
@login_required
def api_ranking(event_id):
    # 获取项目排名
    scores = db.session.query(Score, Athlete, Department).join(
        Athlete, Score.athlete_id == Athlete.athlete_id
    ).join(
        Department, Athlete.dept_id == Department.dept_id
    ).filter(
        Score.event_id == event_id
    ).order_by(Score.ranking).all()

    result = []
    for score, athlete, dept in scores:
        result.append({
            'ranking': score.ranking,
            'athlete_name': athlete.name,
            'student_id': athlete.student_id,
            'dept_name': dept.dept_name,
            'result': score.result,
            'points': score.points
        })

    return jsonify(result)
```

### 6.7 院系排名

**功能描述：**

- 成绩排名计算

**核心实现：**

```python
# 院系排名API
@api_bp.route('/department-ranking')
@login_required
def department_ranking():
    rankings = db.session.query(
        Department.dept_name,
        Department.dept_code,
        func.count(distinct(Athlete.athlete_id)).label('athlete_count'),
        func.count(Score.score_id).label('score_count'),
        func.sum(Score.points).label('total_points')
    ).outerjoin(
        Athlete, Department.dept_id == Athlete.dept_id
    ).outerjoin(
        Score, Athlete.athlete_id == Score.athlete_id
    ).group_by(
        Department.dept_id, Department.dept_name, Department.dept_code
    ).order_by(
        func.sum(Score.points).desc()
    ).all()

    result = []
    for i, (dept_name, dept_code, athlete_count, score_count, total_points) in enumerate(rankings):
        result.append({
            'ranking': i + 1,
            'dept_name': dept_name,
            'dept_code': dept_code,
            'athlete_count': athlete_count,
            'score_count': score_count,
            'total_points': float(total_points) if total_points else 0
        })

    return jsonify(result)
```

---

## 总结

本运动会管理系统成功实现了运动会管理的信息化，通过科学的数据库设计和现代化的技术架构，为运动会管理提供了高效、安全、易用的解决方案。系统在数据完整性、安全性、可扩展性等方面都有良好的表现，能够满足中小型运动会的管理需求。

通过本项目的开发，验证了Flask+MySQL技术栈在Web应用开发中的优势，也展示了规范的数据库设计在系统开发中的重要性。该系统不仅具有实用价值，也为类似的管理系统开发提供了有价值的参考。

**参考文献**

[1] Miguel Grinberg. Flask Web开发：基于Python的Web应用开发实战[M]. 人民邮电出版社, 2018.

[2] 姜承尧. MySQL技术内幕：InnoDB存储引擎[M]. 机械工业出版社, 2013.

[3] 陈卫东. 数据库系统概论[M]. 高等教育出版社, 2014.

[4] Flask官方文档. https://flask.palletsprojects.com/

[5] MySQL官方文档. https://dev.mysql.com/doc/

[6] Bootstrap官方文档. https://getbootstrap.com/docs/
