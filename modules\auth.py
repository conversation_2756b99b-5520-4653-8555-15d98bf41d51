"""
认证模块
"""
from flask import Blueprint, render_template, request, redirect, url_for, flash, session
from flask_wtf import FlaskForm
from wtforms import StringField, PasswordField, SubmitField
from wtforms.validators import DataRequired, Length
from flask_login import login_user, logout_user, login_required, current_user
from functools import wraps
from modules.database import db
from modules.models import User

# 创建认证蓝图
auth_bp = Blueprint('auth', __name__)

# 登录表单类
class LoginForm(FlaskForm):
    """登录表单"""
    username = StringField('用户名', validators=[DataRequired(), Length(max=20)])
    password = PasswordField('密码', validators=[DataRequired(), Length(max=100)])
    submit = SubmitField('登录')

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    """
    用户登录
    
    Returns:
        登录页面或重定向到首页
    """
    # 如果用户已登录，重定向到首页
    if current_user.is_authenticated:
        return redirect(url_for('index'))
    
    # 创建登录表单
    form = LoginForm()
    
    # 处理表单提交
    if form.validate_on_submit():
        # 获取表单数据
        username = form.username.data
        password = form.password.data
        
        # 查询用户
        user = User.query.filter_by(username=username).first()
        
        # 验证用户名和密码
        if user and user.password == password:  # 实际应用中应使用密码哈希
            # 登录用户
            login_user(user)
            
            # 保存用户信息到session
            session['username'] = user.username
            session['role'] = user.role
            
            # 重定向到首页
            next_page = request.args.get('next')
            flash('登录成功！', 'success')
            return redirect(next_page or url_for('index'))
        else:
            # 登录失败
            flash('用户名或密码错误！', 'danger')
    
    # 渲染登录页面
    return render_template('login.html', form=form)

@auth_bp.route('/logout')
@login_required
def logout():
    """
    用户登出
    
    Returns:
        重定向到登录页面
    """
    # 登出用户
    logout_user()
    
    # 清除session
    session.clear()
    
    # 重定向到登录页面
    flash('您已成功登出！', 'success')
    return redirect(url_for('auth.login'))

# 管理员权限装饰器
def admin_required(f):
    """
    管理员权限装饰器
    
    Args:
        f: 被装饰的函数
    
    Returns:
        装饰后的函数
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or session.get('role') != 'admin':
            flash('您没有权限访问该页面！', 'danger')
            return redirect(url_for('auth.login'))
        return f(*args, **kwargs)
    return decorated_function
