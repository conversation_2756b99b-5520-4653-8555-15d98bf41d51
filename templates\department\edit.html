{% extends "layout.html" %}

{% block title %}编辑院系 - 运动会管理系统{% endblock %}

{% block page_title %}编辑院系{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">编辑院系</h5>
    </div>
    <div class="card-body">
        <form method="post" action="{{ url_for('department.edit', dept_id=department.dept_id) }}">
            {{ form.csrf_token }}
            
            <div class="mb-3">
                <label for="dept_name" class="form-label">院系名称</label>
                {{ form.dept_name(class="form-control", id="dept_name") }}
                {% if form.dept_name.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.dept_name.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
            
            <div class="mb-3">
                <label for="dept_code" class="form-label">院系代码</label>
                {{ form.dept_code(class="form-control", id="dept_code") }}
                {% if form.dept_code.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.dept_code.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% endif %}
                <div class="form-text">院系代码必须唯一，用于标识院系</div>
            </div>
            
            <div class="d-flex justify-content-between">
                <a href="{{ url_for('department.index') }}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> 返回
                </a>
                {{ form.submit(class="btn btn-primary") }}
            </div>
        </form>
    </div>
</div>
{% endblock %}
