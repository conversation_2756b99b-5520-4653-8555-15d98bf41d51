"""
配置文件，包含数据库连接信息和其他配置参数
"""
import os
from dotenv import load_dotenv

# 加载.env文件中的环境变量（如果存在）
load_dotenv()

class Config:
    """基础配置类"""
    # 密钥配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev_key_for_sports_meet_system'
    
    # 数据库配置
    MYSQL_USER = os.environ.get('MYSQL_USER') or 'root'
    MYSQL_PASSWORD = os.environ.get('MYSQL_PASSWORD') or '123456'
    MYSQL_HOST = os.environ.get('MYSQL_HOST') or 'localhost'
    MYSQL_PORT = os.environ.get('MYSQL_PORT') or '3306'
    MYSQL_DB = os.environ.get('MYSQL_DB') or 'sports_meetDB'
    
    # SQLAlchemy配置
    SQLALCHEMY_DATABASE_URI = f'mysql://{MYSQL_USER}:{MYSQL_PASSWORD}@{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DB}'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    #SQLALCHEMY_ECHO = True  # 在开发时打印SQL语句

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True

# 配置字典，用于选择运行环境
config = {
    'development': DevelopmentConfig,
    'default': DevelopmentConfig
}
