"""
应用入口文件
"""
import os
import datetime
from flask import Flask, render_template, redirect, url_for, g, request, session
from flask_wtf.csrf import CSRFProtect
from flask_login import LoginManager, current_user

# 导入配置和数据库模块
from config import config
from modules.database import init_app, db

# 导入认证模块
from modules.auth import auth_bp, login_required
from modules.department import department_bp
from modules.athlete import athlete_bp
from modules.event import event_bp
from modules.judge import judge_bp
from modules.score import score_bp
from modules.api import api_bp
from modules.event_athlete import event_athlete_bp


def create_app(config_name='default'):
    """
    创建并配置Flask应用

    Args:
        config_name: 配置名称，默认为'default'

    Returns:
        Flask应用实例
    """
    # 创建Flask应用
    app = Flask(__name__)

    # 加载配置
    app.config.from_object(config[config_name])

    # 初始化CSRF保护
    csrf = CSRFProtect(app)

    # 初始化 SQLAlchemy 数据库
    init_app(app)


    # 初始化Flask-Login
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'

    @login_manager.user_loader
    def load_user(user_id):
        from modules.models import User
        return User.query.filter_by(username=user_id).first()

    # 注意：数据库初始化功能已移除，请确保数据库已手动创建

    # 注册蓝图
    app.register_blueprint(auth_bp, url_prefix='/auth')
    app.register_blueprint(department_bp, url_prefix='/department')
    app.register_blueprint(athlete_bp, url_prefix='/athlete')
    app.register_blueprint(event_bp, url_prefix='/event')
    app.register_blueprint(judge_bp, url_prefix='/judge')
    app.register_blueprint(score_bp, url_prefix='/score')
    app.register_blueprint(api_bp)
    app.register_blueprint(event_athlete_bp)


    # 注册请求前处理器，添加当前时间到g对象
    @app.before_request
    def before_request():
        g.now = datetime.datetime.now()

    # 注册模板上下文处理器，使模板可以访问session和now
    @app.context_processor
    def inject_context():
        return {
            'session': session,
            'now': g.now if hasattr(g, 'now') else datetime.datetime.now(),
            'current_user': current_user
        }

    # 注册路由
    @app.route('/')
    @login_required
    def index():
        """主页路由，需要登录"""
        return render_template('index.html')


    return app

if __name__ == '__main__':
    app = create_app()
    app.run(debug=True)
