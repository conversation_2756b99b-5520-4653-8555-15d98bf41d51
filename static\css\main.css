/* 运动会管理系统 - 主样式表 */

:root {
    /* 主色调 */
    --primary-color: #4759b8;
    --primary-hover: #3949a2;
    --primary-light: #eef0fb;
    --secondary-color: #36cfc9;
    --secondary-hover: #29b6b1;

    /* 中性色调 */
    --dark-color: #1c2033;
    --text-color: #3e4155;
    --text-secondary: #7a7f9a;
    --border-color: #e2e8f0;
    --light-bg: #f8f9fc;

    /* 状态色调 */
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #3b82f6;

    /* 布局变量 */
    --header-height: 60px;
    --sidebar-width: 260px;
    --sidebar-collapsed-width: 70px;
    --border-radius: 8px;
    --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);

    /* 动画速度 */
    --transition-speed: 0.3s;
}

/* 基础样式 */
body {
    font-family: "Microsoft YaHei", -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON>o, "Helvetica Neue", Arial, sans-serif;
    background-color: var(--light-bg);
    color: var(--text-color);
    overflow-x: hidden;
    font-size: 0.95rem;
    margin: 0;
    padding: 0;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 页面加载动画 */
body:not(.loaded) .content-wrapper {
    opacity: 0;
}

body.loaded .content-wrapper {
    opacity: 1;
    transition: opacity 0.5s ease;
}

/* 头部样式 */
.header-container {
    height: var(--header-height);
    background-color: #fff;
    border-bottom: 1px solid var(--border-color);
    padding: 0;
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    z-index: 1030;
}

.header-container .container-fluid {
    padding: 0 1.5rem;
    height: 100%;
}

.user-info {
    display: flex;
    align-items: center;
    color: var(--text-color);
    font-weight: 500;
}

.user-info i {
    font-size: 1.2rem;
    color: var(--primary-color);
}

.user-info .user-name {
    margin-left: 0.5rem;
}

.user-info .dropdown-toggle::after {
    margin-left: 0.5rem;
}

/* 侧边栏样式 */
.sidebar {
    width: var(--sidebar-width);
    background-color: #fff;
    border-right: 1px solid var(--border-color);
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1040;
    transition: width var(--transition-speed) ease;
    overflow-y: auto;
    box-shadow: var(--box-shadow);
}

.sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

.sidebar-brand {
    height: var(--header-height);
    display: flex;
    align-items: center;
    padding: 0 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.sidebar-brand h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--primary-color);
    white-space: nowrap;
    overflow: hidden;
}

.sidebar-divider {
    height: 1px;
    background-color: var(--border-color);
    margin: 0.5rem 0;
}

.sidebar-nav {
    padding: 1rem 0;
}

.sidebar-nav .nav-item {
    margin-bottom: 0.25rem;
}

.sidebar-nav .nav-link {
    padding: 0.75rem 1.5rem;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    border-radius: 0;
    transition: all var(--transition-speed) ease;
}

.sidebar-nav .nav-link i {
    font-size: 1.1rem;
    margin-right: 0.75rem;
    width: 1.5rem;
    text-align: center;
}

.sidebar-nav .nav-link span {
    white-space: nowrap;
    overflow: hidden;
}

.sidebar-nav .nav-link:hover {
    color: var(--primary-color);
    background-color: var(--primary-light);
}

.sidebar-nav .nav-link.active {
    color: var(--primary-color);
    background-color: var(--primary-light);
    font-weight: 500;
}

.sidebar.collapsed .sidebar-brand h3 {
    display: none;
}

.sidebar.collapsed .nav-link span {
    display: none;
}

.sidebar.collapsed .nav-link {
    padding: 0.75rem;
    justify-content: center;
}

.sidebar.collapsed .nav-link i {
    margin-right: 0;
}

/* 内容区域样式 */
.content-wrapper {
    margin-left: var(--sidebar-width);
    padding: calc(var(--header-height) + 1.5rem) 1.5rem 1.5rem;
    min-height: 100vh;
    transition: margin-left var(--transition-speed) ease;
}

.content-wrapper.expanded {
    margin-left: var(--sidebar-collapsed-width);
}

/* 页面标题 */
.page-title {
    margin-bottom: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.page-title h1 {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
    color: var(--dark-color);
}

.breadcrumb {
    margin-bottom: 0;
    background-color: transparent;
    padding: 0;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: ">";
}

/* 卡片样式 */
.card {
    background-color: #fff;
    border-radius: var(--border-radius);
    border: none;
    box-shadow: var(--box-shadow);
    margin-bottom: 1.5rem;
    transition: transform var(--transition-speed) ease, box-shadow var(--transition-speed) ease;
}

.card:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.card-header {
    background-color: transparent;
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 1.25rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-header h5 {
    margin: 0;
    font-weight: 600;
}

.card-body {
    padding: 1.25rem;
}

/* 统计卡片 */
.stat-card {
    background-color: #fff;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--box-shadow);
    transition: transform var(--transition-speed) ease, box-shadow var(--transition-speed) ease;
    position: relative;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
}

.department-card::before {
    background-color: var(--primary-color);
}

.athlete-card::before {
    background-color: var(--secondary-color);
}

.event-card::before {
    background-color: var(--success-color);
}

.judge-card::before {
    background-color: var(--warning-color);
}

.score-card::before {
    background-color: var(--info-color);
}

.stat-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.athlete-card .stat-icon {
    color: var(--secondary-color);
}

.event-card .stat-icon {
    color: var(--success-color);
}

.judge-card .stat-icon {
    color: var(--warning-color);
}

.score-card .stat-icon {
    color: var(--info-color);
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.stat-label {
    font-size: 1rem;
    color: var(--text-secondary);
    margin-bottom: 0;
}

.stat-progress {
    margin-top: auto;
    padding-top: 1rem;
}

.progress {
    height: 8px;
    background-color: var(--light-bg);
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    transition: width 0.5s ease;
}

.progress-bar-department {
    background-color: var(--primary-color);
}

.progress-bar-athlete {
    background-color: var(--secondary-color);
}

.progress-bar-event {
    background-color: var(--success-color);
}

.progress-bar-judge {
    background-color: var(--warning-color);
}

.progress-bar-score {
    background-color: var(--info-color);
}

/* 表格样式 */
.table {
    margin-bottom: 0;
}

.table th {
    font-weight: 600;
    color: var(--text-color);
    border-top: none;
    padding: 0.75rem 1rem;
}

.table td {
    padding: 0.75rem 1rem;
    vertical-align: middle;
}

.table-hover tbody tr:hover {
    background-color: var(--primary-light);
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

/* 按钮样式 */
.btn {
    padding: 0.5rem 1rem;
    font-weight: 500;
    border-radius: var(--border-radius);
    transition: all var(--transition-speed) ease;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
}

.btn-secondary {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-secondary:hover {
    background-color: var(--secondary-hover);
    border-color: var(--secondary-hover);
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-warning {
    background-color: var(--warning-color);
    border-color: var(--warning-color);
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

.btn-info {
    background-color: var(--info-color);
    border-color: var(--info-color);
}

.btn-icon {
    width: 36px;
    height: 36px;
    padding: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.btn-icon i {
    font-size: 1rem;
}

/* 表单样式 */
.form-control {
    border-radius: var(--border-radius);
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--border-color);
    transition: border-color var(--transition-speed) ease, box-shadow var(--transition-speed) ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(71, 89, 184, 0.25);
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: var(--text-color);
}

/* 加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.3s ease;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(71, 89, 184, 0.1);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-animation {
    width: 100%;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading-animation::after {
    content: '';
    width: 40px;
    height: 40px;
    border: 4px solid rgba(71, 89, 184, 0.1);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s linear infinite;
}

/* 欢迎区域 */
.welcome-section {
    margin-bottom: 1.5rem;
    padding: 1.5rem;
    background-color: #fff;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.welcome-title {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.welcome-subtitle {
    font-size: 1rem;
    color: var(--text-secondary);
    margin-bottom: 0.75rem;
}

.current-date {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-bottom: 0;
}

/* 数据区域 */
.data-section {
    margin-bottom: 2rem;
}

.data-section-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.data-section-title h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
}

.data-section-link {
    color: var(--primary-color);
    font-weight: 500;
    text-decoration: none;
    display: flex;
    align-items: center;
}

.data-section-link i {
    margin-left: 0.5rem;
}

.data-section-link:hover {
    color: var(--primary-hover);
}

.data-card {
    background-color: #fff;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

/* 动画效果 */
.fade-in-up {
    animation: fadeInUp 0.5s ease forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式调整 */
@media (max-width: 992px) {
    .sidebar {
        transform: translateX(-100%);
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .content-wrapper {
        margin-left: 0;
    }

    .content-wrapper.expanded {
        margin-left: 0;
    }

    .header-container {
        left: 0;
    }
}

@media (max-width: 768px) {
    .welcome-title {
        font-size: 1.5rem;
    }

    .stat-number {
        font-size: 1.75rem;
    }

    .stat-icon {
        font-size: 2rem;
    }

    .card-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .card-header .btn {
        margin-top: 0.5rem;
    }
}

@media (max-width: 576px) {
    .content-wrapper {
        padding: calc(var(--header-height) + 1rem) 1rem 1rem;
    }

    .welcome-section {
        padding: 1rem;
    }

    .stat-card {
        padding: 1rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .stat-icon {
        font-size: 1.75rem;
    }
}

/* 通知样式 */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    max-width: 350px;
}

.notification {
    background-color: #fff;
    border-radius: var(--border-radius);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    margin-bottom: 10px;
    padding: 15px;
    display: flex;
    align-items: flex-start;
    width: 100%;
    transform: translateX(100%);
    opacity: 0;
    transition: transform 0.3s ease, opacity 0.3s ease;
    overflow: hidden;
    position: relative;
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification.hiding {
    transform: translateX(100%);
    opacity: 0;
}

.notification-icon {
    margin-right: 15px;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-content {
    flex: 1;
    font-size: 0.95rem;
    color: var(--text-color);
    padding-right: 20px;
}

.notification-close {
    position: absolute;
    top: 10px;
    right: 10px;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0;
    font-size: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    transition: background-color 0.2s ease;
}

.notification-close:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.notification-success .notification-icon {
    color: var(--success-color);
}

.notification-info .notification-icon {
    color: var(--info-color);
}

.notification-warning .notification-icon {
    color: var(--warning-color);
}

.notification-danger .notification-icon {
    color: var(--danger-color);
}

/* 打印样式 */
@media print {
    .sidebar, .header-container {
        display: none !important;
    }

    .content-wrapper {
        margin-left: 0 !important;
        padding: 0 !important;
    }

    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }

    .no-print {
        display: none !important;
    }
}
