"""
院系管理模块
"""
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_wtf import FlaskForm
from wtforms import StringField, SubmitField
from wtforms.validators import DataRequired, Length
from modules.database import db
from modules.models import Department
from modules.auth import login_required, admin_required

# 创建院系蓝图
department_bp = Blueprint('department', __name__)

# 院系表单类
class DepartmentForm(FlaskForm):
    """院系表单类，用于添加和编辑院系信息"""
    dept_name = StringField('院系名称', validators=[DataRequired(), Length(max=50)])
    dept_code = StringField('院系代码', validators=[DataRequired(), Length(max=10)])
    submit = SubmitField('提交')

@department_bp.route('/')
@login_required
def index():
    """
    院系列表页面

    Returns:
        院系列表页面
    """
    # 使用标准表格组件
    return render_template('department/index.html')

@department_bp.route('/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add():
    """
    添加院系

    Returns:
        添加院系页面或重定向到院系列表页面
    """
    # 创建表单
    form = DepartmentForm()

    # 处理表单提交
    if form.validate_on_submit():
        # 获取表单数据
        dept_name = form.dept_name.data
        dept_code = form.dept_code.data

        # 检查院系代码是否已存在
        existing_dept = Department.query.filter_by(dept_code=dept_code).first()
        if existing_dept:
            flash('院系代码已存在！', 'danger')
            return render_template('department/add.html', form=form)

        # 创建院系
        department = Department(dept_name=dept_name, dept_code=dept_code)

        # 保存到数据库
        db.session.add(department)
        db.session.commit()

        # 重定向到院系列表页面
        flash('院系添加成功！', 'success')
        return redirect(url_for('department.index'))

    # 渲染添加院系页面
    return render_template('department/add.html', form=form)

@department_bp.route('/edit/<int:dept_id>', methods=['GET', 'POST'])
@login_required
@admin_required
def edit(dept_id):
    """
    编辑院系

    Args:
        dept_id: 院系ID

    Returns:
        编辑院系页面或重定向到院系列表页面
    """
    # 查询院系
    department = Department.query.get_or_404(dept_id)

    # 创建表单
    form = DepartmentForm(obj=department)

    # 处理表单提交
    if form.validate_on_submit():
        # 获取表单数据
        dept_name = form.dept_name.data
        dept_code = form.dept_code.data

        # 检查院系代码是否已存在（排除当前院系）
        existing_dept = Department.query.filter(Department.dept_code == dept_code, Department.dept_id != dept_id).first()
        if existing_dept:
            flash('院系代码已存在！', 'danger')
            return render_template('department/edit.html', form=form, department=department)

        # 更新院系
        department.dept_name = dept_name
        department.dept_code = dept_code

        # 保存到数据库
        db.session.commit()

        # 重定向到院系列表页面
        flash('院系更新成功！', 'success')
        return redirect(url_for('department.index'))

    # 渲染编辑院系页面
    return render_template('department/edit.html', form=form, department=department)

@department_bp.route('/delete/<int:dept_id>', methods=['POST'])
@login_required
@admin_required
def delete(dept_id):
    """
    删除院系

    Args:
        dept_id: 院系ID

    Returns:
        重定向到院系列表页面
    """
    # 查询院系
    department = Department.query.get_or_404(dept_id)

    # 检查是否有关联的运动员
    if department.athletes:
        flash('无法删除院系，因为有关联的运动员！', 'danger')
        return redirect(url_for('department.index'))

    # 删除院系
    db.session.delete(department)
    db.session.commit()

    # 重定向到院系列表页面
    flash('院系删除成功！', 'success')
    return redirect(url_for('department.index'))

@department_bp.route('/api/list')
@login_required
def api_list():
    """
    院系列表API
    用于AJAX请求获取院系列表

    Returns:
        JSON格式的院系列表
    """
    departments = Department.query.all()
    result = [{
        'dept_id': department.dept_id,
        'dept_name': department.dept_name,
        'dept_code': department.dept_code,
        'athlete_count': len(department.athletes)
    } for department in departments]

    return jsonify(result)
