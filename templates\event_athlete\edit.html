{% extends "layout.html" %}

{% block title %}编辑学生成绩 - 运动会管理系统{% endblock %}

{% block page_title %}编辑学生成绩{% endblock %}

{% block content %}
<div class="card shadow-sm">
    <div class="card-header bg-white py-3">
        <h5 class="mb-0 text-primary">
            <i class="bi bi-pencil-square me-2"></i>编辑学生成绩
        </h5>
    </div>
    <div class="card-body">
        <div class="alert alert-info mb-4">
            <div class="d-flex">
                <div class="me-3">
                    <i class="bi bi-info-circle-fill" style="font-size: 1.5rem;"></i>
                </div>
                <div>
                    <h5 class="alert-heading">学生信息</h5>
                    <p class="mb-0">
                        <strong>姓名:</strong> {{ score.athlete.name }} | 
                        <strong>学号:</strong> {{ score.athlete.student_id }} | 
                        <strong>性别:</strong> {{ score.athlete.gender }} | 
                        <strong>院系:</strong> {{ score.athlete.department.dept_name }}
                    </p>
                </div>
            </div>
        </div>
        
        <div class="alert alert-secondary mb-4">
            <div class="d-flex">
                <div class="me-3">
                    <i class="bi bi-calendar-event-fill" style="font-size: 1.5rem;"></i>
                </div>
                <div>
                    <h5 class="alert-heading">比赛项目信息</h5>
                    <p class="mb-0">
                        <strong>项目名称:</strong> {{ score.event.event_name }} | 
                        <strong>项目类型:</strong> {{ score.event.event_type }} | 
                        <strong>开始时间:</strong> {{ score.event.start_time.strftime('%Y-%m-%d %H:%M') if score.event.start_time else '未设置' }} | 
                        <strong>比赛地点:</strong> {{ score.event.location or '未设置' }}
                    </p>
                </div>
            </div>
        </div>
        
        <form method="post" action="{{ url_for('event_athlete.edit', score_id=score.score_id) }}">
            {{ form.csrf_token }}
            
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="result" class="form-label">成绩</label>
                        {{ form.result(class="form-control", id="result", placeholder="例如: 10.5秒, 5.2米") }}
                        {% if form.result.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.result.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">填写具体成绩</div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="ranking" class="form-label">排名</label>
                        {{ form.ranking(class="form-control", id="ranking", placeholder="例如: 1, 2, 3") }}
                        {% if form.ranking.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.ranking.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">填写排名</div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="points" class="form-label">得分</label>
                        {{ form.points(class="form-control", id="points", placeholder="例如: 5, 3, 1") }}
                        {% if form.points.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.points.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">填写得分</div>
                    </div>
                </div>
            </div>
            
            <div class="d-flex justify-content-between mt-4">
                <a href="{{ url_for('event_athlete.index', event_id=score.event_id) }}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> 返回
                </a>
                {{ form.submit(class="btn btn-primary") }}
            </div>
        </form>
    </div>
</div>
{% endblock %}
