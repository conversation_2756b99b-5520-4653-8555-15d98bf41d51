/**
 * 运动会管理系统 - 主JS文件
 */

document.addEventListener('DOMContentLoaded', function() {
    // 显示页面加载动画
    showPageLoadingAnimation();

    // 初始化全局AJAX设置
    initAjaxSetup();

    // 初始化Bootstrap组件
    initBootstrapComponents();

    // 初始化通用事件监听
    initEventListeners();

    // 初始化数据表格 (如果存在)
    initDataTables();

    // 初始化内容区域的动画效果
    initContentAnimations();

    // 隐藏页面加载动画
    setTimeout(hidePageLoadingAnimation, 500);
});

/**
 * 显示页面加载动画
 */
function showPageLoadingAnimation() {
    const overlay = document.querySelector('.loading-overlay');
    if (overlay) {
        overlay.classList.remove('d-none');
    }
}

/**
 * 隐藏页面加载动画
 */
function hidePageLoadingAnimation() {
    const overlay = document.querySelector('.loading-overlay');
    if (overlay) {
        overlay.classList.add('d-none');
    }
}

/**
 * 初始化全局AJAX设置
 */
function initAjaxSetup() {
    // 为所有AJAX请求添加CSRF令牌
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
    if (csrfToken) {
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': csrfToken
            }
        });

        // 添加请求拦截器显示加载动画
        $(document).ajaxStart(function() {
            showPageLoadingAnimation();
        });

        $(document).ajaxStop(function() {
            setTimeout(hidePageLoadingAnimation, 300);
        });
    }

    // 为Fetch API请求添加拦截器
    const originalFetch = window.fetch;
    window.fetch = function() {
        showPageLoadingAnimation();
        return originalFetch.apply(this, arguments)
            .then(response => {
                setTimeout(hidePageLoadingAnimation, 300);
                return response;
            })
            .catch(error => {
                setTimeout(hidePageLoadingAnimation, 300);
                throw error;
            });
    };
}

/**
 * 初始化Bootstrap组件
 */
function initBootstrapComponents() {
    // 初始化所有工具提示
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // 初始化所有弹出框
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
}

/**
 * 初始化通用事件监听
 */
function initEventListeners() {
    // 删除确认
    document.querySelectorAll('.delete-confirm').forEach(function(element) {
        element.addEventListener('click', function(e) {
            if (!confirm('确定要删除吗？此操作不可撤销！')) {
                e.preventDefault();
            }
        });
    });

    // 自动隐藏警告消息
    document.querySelectorAll('.alert').forEach(function(alert) {
        setTimeout(function() {
            var bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 5000);
    });
}

/**
 * 初始化数据表格
 */
function initDataTables() {
    // 检查是否存在DataTables
    if (typeof $.fn?.DataTable !== 'undefined') {
        // 默认DataTables配置
        $.extend(true, $.fn.DataTable.defaults, {
            language: {
                url: "/static/js/dataTables.chinese.json"
            },
            pageLength: 10,
            lengthMenu: [[10, 25, 50, -1], [10, 25, 50, "全部"]],
            responsive: true,
            autoWidth: false,
            processing: true,
            searching: true,
            stateSave: true,
            dom: "<'row'<'col-sm-12 col-md-6'l><'col-sm-12 col-md-6'f>>" +
                 "<'row'<'col-sm-12'tr>>" +
                 "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
            initComplete: function() {
                // 给表格添加响应式类
                $(this).closest('.dataTables_wrapper').addClass('table-responsive mb-0');
            }
        });
        console.log('DataTables 已初始化，使用本地中文语言文件');
    } else {
        console.log('DataTables 未加载，使用原生 Bootstrap 表格');
    }
}

/**
 * 初始化内容区域的动画效果
 */
function initContentAnimations() {
    // 添加淡入效果
    document.querySelectorAll('.fade-in').forEach(function(element) {
        element.classList.add('animate__animated', 'animate__fadeIn');
    });

    // 为卡片添加淡入上移效果
    const cards = document.querySelectorAll('.card, .stat-card, .data-card');
    cards.forEach((card, index) => {
        setTimeout(() => {
            card.classList.add('fade-in-up');
        }, 100 * index);
    });

    // 为表格行添加淡入效果
    const tables = document.querySelectorAll('.table');
    tables.forEach(table => {
        const rows = table.querySelectorAll('tbody tr');
        rows.forEach((row, index) => {
            row.style.opacity = '0';
            row.style.transform = 'translateY(10px)';
            row.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
            setTimeout(() => {
                row.style.opacity = '1';
                row.style.transform = 'translateY(0)';
            }, 50 * (index + 1));
        });
    });
}

/**
 * 格式化日期时间
 * @param {string} dateString - 日期字符串
 * @returns {string} - 格式化后的日期字符串
 */
function formatDateTime(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

/**
 * 加载数据表格数据
 * @param {string} url - API URL
 * @param {string} tableId - 表格ID
 * @param {Function} rowFormatter - 行格式化函数
 */
function loadTableData(url, tableId, rowFormatter) {
    const tableBody = document.getElementById(tableId);
    if (!tableBody) return;

    // 显示加载动画
    tableBody.innerHTML = '<tr><td colspan="100%" class="text-center"><div class="loading-animation"></div></td></tr>';

    fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            tableBody.innerHTML = '';

            if (data.length === 0) {
                const row = document.createElement('tr');
                row.innerHTML = '<td colspan="100%" class="text-center">暂无数据</td>';
                tableBody.appendChild(row);
            } else {
                data.forEach((item, index) => {
                    const row = document.createElement('tr');
                    row.style.opacity = '0';
                    row.style.transform = 'translateY(10px)';
                    row.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                    row.innerHTML = rowFormatter(item);
                    tableBody.appendChild(row);

                    // 添加渐进动画效果
                    setTimeout(() => {
                        row.style.opacity = '1';
                        row.style.transform = 'translateY(0)';
                    }, 50 * (index + 1));
                });
            }
        })
        .catch(error => {
            console.error('Error loading data:', error);
            tableBody.innerHTML = '<tr><td colspan="100%" class="text-center text-danger"><i class="bi bi-exclamation-triangle me-2"></i>加载数据失败</td></tr>';
        });
}

/**
 * 创建确认对话框
 * @param {string} title - 标题
 * @param {string} message - 消息
 * @param {Function} onConfirm - 确认回调
 * @param {Function} onCancel - 取消回调
 */
function createConfirmDialog(title, message, onConfirm, onCancel) {
    // 创建对话框元素
    const dialogId = 'confirm-dialog-' + Date.now();
    const dialogHtml = `
        <div class="modal fade" id="${dialogId}" tabindex="-1" aria-labelledby="${dialogId}-label" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="${dialogId}-label">${title}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                    </div>
                    <div class="modal-body">
                        ${message}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary confirm-btn">确认</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 添加到文档中
    document.body.insertAdjacentHTML('beforeend', dialogHtml);

    // 获取对话框元素
    const dialogElement = document.getElementById(dialogId);
    const modal = new bootstrap.Modal(dialogElement);

    // 添加事件监听器
    dialogElement.querySelector('.confirm-btn').addEventListener('click', function() {
        if (typeof onConfirm === 'function') {
            onConfirm();
        }
        modal.hide();
    });

    dialogElement.addEventListener('hidden.bs.modal', function() {
        // 对话框关闭后移除元素
        dialogElement.remove();
    });

    // 显示对话框
    modal.show();

    // 当对话框关闭且未点击确认时调用取消回调
    dialogElement.addEventListener('hidden.bs.modal', function(event) {
        if (event.target.querySelector('.confirm-btn:not(.clicked)') && typeof onCancel === 'function') {
            onCancel();
        }
    });
}

/**
 * 显示通知消息
 * @param {string} message - 消息内容
 * @param {string} type - 消息类型 (success, info, warning, danger)
 * @param {number} duration - 显示时间 (毫秒)
 */
function showNotification(message, type = 'info', duration = 3000) {
    // 创建通知元素
    const notificationId = 'notification-' + Date.now();
    const notificationHtml = `
        <div id="${notificationId}" class="notification notification-${type}">
            <div class="notification-icon">
                <i class="bi ${getIconForType(type)}"></i>
            </div>
            <div class="notification-content">
                ${message}
            </div>
            <button type="button" class="notification-close">
                <i class="bi bi-x"></i>
            </button>
        </div>
    `;

    // 确保通知容器存在
    let container = document.querySelector('.notification-container');
    if (!container) {
        container = document.createElement('div');
        container.className = 'notification-container';
        document.body.appendChild(container);
    }

    // 添加通知到容器
    container.insertAdjacentHTML('beforeend', notificationHtml);

    // 获取通知元素
    const notification = document.getElementById(notificationId);

    // 添加关闭按钮事件
    notification.querySelector('.notification-close').addEventListener('click', function() {
        closeNotification(notification);
    });

    // 显示通知
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);

    // 设置自动关闭
    if (duration > 0) {
        setTimeout(() => {
            closeNotification(notification);
        }, duration);
    }

    // 返回通知元素
    return notification;
}

/**
 * 关闭通知
 * @param {HTMLElement} notification - 通知元素
 */
function closeNotification(notification) {
    notification.classList.remove('show');
    notification.classList.add('hiding');

    // 动画结束后移除元素
    setTimeout(() => {
        notification.remove();

        // 如果容器中没有更多通知，则移除容器
        const container = document.querySelector('.notification-container');
        if (container && container.children.length === 0) {
            container.remove();
        }
    }, 300);
}

/**
 * 根据类型获取图标类
 * @param {string} type - 消息类型
 * @returns {string} - 图标类
 */
function getIconForType(type) {
    switch (type) {
        case 'success':
            return 'bi-check-circle-fill';
        case 'warning':
            return 'bi-exclamation-triangle-fill';
        case 'danger':
            return 'bi-x-circle-fill';
        case 'info':
        default:
            return 'bi-info-circle-fill';
    }
}

/**
 * 格式化数字为带千位分隔符的字符串
 * @param {number} number - 要格式化的数字
 * @returns {string} - 格式化后的字符串
 */
function formatNumber(number) {
    return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}
