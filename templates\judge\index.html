{% extends "layout.html" %}

{% block title %}裁判员管理 - 运动会管理系统{% endblock %}

{% block page_title %}裁判员管理{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">裁判员列表</h5>
        {% if session.role == 'admin' %}
        <a href="{{ url_for('judge.add') }}" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> 添加裁判员
        </a>
        {% endif %}
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover datatable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>姓名</th>
                        <th>级别</th>
                        <th>联系方式</th>
                        <th>关联项目数</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="judge-table">
                    <tr>
                        <td colspan="6" class="text-center">加载中...</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 加载裁判员数据
        loadJudgeData();
    });

    // 加载裁判员数据
    function loadJudgeData() {
        fetch('/judge/api/list')
            .then(response => response.json())
            .then(data => {
                const tableBody = document.getElementById('judge-table');
                tableBody.innerHTML = '';

                if (data.length === 0) {
                    const row = document.createElement('tr');
                    row.innerHTML = '<td colspan="6" class="text-center">暂无裁判员数据</td>';
                    tableBody.appendChild(row);
                } else {
                    data.forEach(judge => {
                        const row = document.createElement('tr');

                        // 构建操作按钮
                        let actions = `
                            <a href="{{ url_for('judge.edit', judge_id=0) }}${judge.judge_id}" class="btn btn-sm btn-primary me-1"><i class="bi bi-pencil"></i> 编辑</a>
                        `;

                        // 只有没有关联项目的裁判员才能删除
                        if (judge.event_count === 0) {
                            actions += `
                                <form method="POST" action="{{ url_for('judge.delete', judge_id=0) }}${judge.judge_id}" class="d-inline">
                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                    <button type="submit" class="btn btn-sm btn-danger delete-confirm"><i class="bi bi-trash"></i> 删除</button>
                                </form>
                            `;
                        } else {
                            actions += `<button class="btn btn-sm btn-danger" disabled title="有关联的比赛项目，无法删除"><i class="bi bi-trash"></i> 删除</button>`;
                        }

                        row.innerHTML = `
                            <td>${judge.judge_id}</td>
                            <td>${judge.name}</td>
                            <td>${judge.level || '-'}</td>
                            <td>${judge.contact || '-'}</td>
                            <td>${judge.event_count}</td>
                            <td>${actions}</td>
                        `;

                        tableBody.appendChild(row);
                    });

                    // 初始化删除确认
                    document.querySelectorAll('.delete-confirm').forEach(function(button) {
                        button.addEventListener('click', function(e) {
                            if (!confirm('确定要删除该裁判员吗？此操作不可撤销！')) {
                                e.preventDefault();
                            }
                        });
                    });

                    // 初始化DataTable
                    if ($.fn.DataTable.isDataTable('.datatable')) {
                        $('.datatable').DataTable().destroy();
                    }

                    $('.datatable').DataTable({
                        language: {
                            url: '/static/js/dataTables.chinese.json'
                        },
                        responsive: true,
                        pageLength: 10,
                        lengthMenu: [[10, 25, 50, -1], [10, 25, 50, "全部"]]
                    });
                }
            })
            .catch(error => {
                console.error('Error loading judge data:', error);
                const tableBody = document.getElementById('judge-table');
                tableBody.innerHTML = '<tr><td colspan="6" class="text-center text-danger">加载数据失败</td></tr>';
            });
    }
</script>
{% endblock %}
