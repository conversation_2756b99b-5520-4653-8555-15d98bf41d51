"""
成绩管理模块
"""
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_wtf import FlaskForm
from wtforms import StringField, SelectField, IntegerField, SubmitField
from wtforms.validators import DataRequired, Optional, NumberRange
from modules.database import db
from modules.models import Score, Athlete, Event
from modules.auth import login_required, admin_required
from sqlalchemy import func

# 创建成绩蓝图
score_bp = Blueprint('score', __name__)

# 成绩表单类
class ScoreForm(FlaskForm):
    """成绩表单类，用于添加和编辑成绩信息"""
    athlete_id = SelectField('运动员', coerce=int, validators=[DataRequired()])
    event_id = SelectField('比赛项目', coerce=int, validators=[DataRequired()])
    result = StringField('成绩', validators=[DataRequired(), DataRequired()])
    ranking = IntegerField('名次', validators=[Optional(), NumberRange(min=1)])
    points = IntegerField('得分', validators=[Optional(), NumberRange(min=0)])
    submit = SubmitField('提交')

    def __init__(self, *args, **kwargs):
        super(ScoreForm, self).__init__(*args, **kwargs)
        self.athlete_id.choices = [(athlete.athlete_id, f"{athlete.student_id} - {athlete.name}") for athlete in Athlete.query.all()]
        self.event_id.choices = [(event.event_id, event.event_name) for event in Event.query.all()]

@score_bp.route('/')
@login_required
def index():
    """
    成绩列表页面

    Returns:
        成绩列表页面
    """
    # 使用标准表格组件
    return render_template('score/index.html')

@score_bp.route('/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add():
    """
    添加成绩

    Returns:
        添加成绩页面或重定向到成绩列表页面
    """
    # 创建表单
    form = ScoreForm()

    # 处理表单提交
    if form.validate_on_submit():
        # 获取表单数据
        athlete_id = form.athlete_id.data
        event_id = form.event_id.data
        result = form.result.data
        ranking = form.ranking.data
        points = form.points.data

        # 检查运动员是否已有该项目的成绩
        existing_score = Score.query.filter_by(athlete_id=athlete_id, event_id=event_id).first()
        if existing_score:
            flash('该运动员已有此项目的成绩记录！', 'danger')
            return render_template('score/add.html', form=form)

        # 检查性别限制
        athlete = Athlete.query.get(athlete_id)
        event = Event.query.get(event_id)
        if event.gender_limit != '不限' and athlete.gender != event.gender_limit:
            flash(f'该比赛项目仅限{event.gender_limit}性别参加！', 'danger')
            return render_template('score/add.html', form=form)

        # 创建成绩
        score = Score(
            athlete_id=athlete_id,
            event_id=event_id,
            result=result,
            ranking=ranking,
            points=points
        )

        # 保存到数据库
        db.session.add(score)
        db.session.commit()

        # 重定向到成绩列表页面
        flash('成绩添加成功！', 'success')
        return redirect(url_for('score.index'))

    # 渲染添加成绩页面
    return render_template('score/add.html', form=form)

@score_bp.route('/edit/<int:score_id>', methods=['GET', 'POST'])
@login_required
@admin_required
def edit(score_id):
    """
    编辑成绩

    Args:
        score_id: 成绩ID

    Returns:
        编辑成绩页面或重定向到成绩列表页面
    """
    # 查询成绩
    score = Score.query.get_or_404(score_id)

    # 创建表单
    form = ScoreForm(obj=score)

    # 禁用运动员和比赛项目选择（不允许修改）
    form.athlete_id.render_kw = {'disabled': 'disabled'}
    form.event_id.render_kw = {'disabled': 'disabled'}

    # 处理表单提交
    if form.validate_on_submit():
        # 获取表单数据
        result = form.result.data
        ranking = form.ranking.data
        points = form.points.data

        # 更新成绩
        score.result = result
        score.ranking = ranking
        score.points = points

        # 保存到数据库
        db.session.commit()

        # 重定向到成绩列表页面
        flash('成绩更新成功！', 'success')
        return redirect(url_for('score.index'))

    # 设置默认值
    form.athlete_id.data = score.athlete_id
    form.event_id.data = score.event_id

    # 渲染编辑成绩页面
    return render_template('score/edit.html', form=form, score=score)

@score_bp.route('/delete/<int:score_id>', methods=['POST'])
@login_required
@admin_required
def delete(score_id):
    """
    删除成绩

    Args:
        score_id: 成绩ID

    Returns:
        重定向到成绩列表页面
    """
    # 查询成绩
    score = Score.query.get_or_404(score_id)

    # 删除成绩
    db.session.delete(score)
    db.session.commit()

    # 重定向到成绩列表页面
    flash('成绩删除成功！', 'success')
    return redirect(url_for('score.index'))

@score_bp.route('/api/list')
@login_required
def api_list():
    """
    成绩列表API
    用于AJAX请求获取成绩列表

    Returns:
        JSON格式的成绩列表
    """
    scores = Score.query.all()
    result = [{
        'score_id': score.score_id,
        'athlete_id': score.athlete_id,
        'athlete_name': score.athlete.name,
        'student_id': score.athlete.student_id,
        'dept_name': score.athlete.department.dept_name,
        'event_id': score.event_id,
        'event_name': score.event.event_name,
        'event_type': score.event.event_type,
        'result': score.result,
        'ranking': score.ranking,
        'points': score.points,
        'record_time': score.record_time.strftime('%Y-%m-%d %H:%M:%S')
    } for score in scores]

    return jsonify(result)

@score_bp.route('/department_ranking')
@login_required
def department_ranking():
    """
    院系排名页面

    Returns:
        院系排名页面
    """
    # 查询各院系的总得分
    department_scores = db.session.query(
        Athlete.dept_id,
        func.sum(Score.points).label('total_points')
    ).join(Score, Score.athlete_id == Athlete.athlete_id) \
     .group_by(Athlete.dept_id) \
     .order_by(func.sum(Score.points).desc()) \
     .all()

    # 获取院系信息
    from modules.models import Department
    rankings = []
    for dept_id, total_points in department_scores:
        department = Department.query.get(dept_id)
        if department:
            rankings.append({
                'dept_id': dept_id,
                'dept_name': department.dept_name,
                'dept_code': department.dept_code,
                'total_points': total_points or 0
            })

    return render_template('score/department_ranking.html', rankings=rankings)
